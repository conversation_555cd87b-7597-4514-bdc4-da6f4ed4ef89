import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { rehabilitationAPI, patientsAPI } from '../services/api';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  MenuItem,
  Paper,
  Select,
  Tab,
  Tabs,
  TextField,
  Typography,
  CircularProgress,
  Alert,
  Stack,
  Chip,
  Avatar,
  Badge,
  Tooltip,
  Collapse,
  useTheme,
  alpha,
  Fade,
  Zoom,
  Fab,
  useMediaQuery,
} from '@mui/material';
// Define the SelectChangeEvent type inline
type SelectChangeEvent = {
  target: {
    value: string;
    name: string;
  };
};
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FitnessCenter as FitnessCenterIcon,
  Assignment as AssignmentIcon,
  Timeline as TimelineIcon,
  Person as PersonIcon,
  MoreVert as MoreVertIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  LocalHospital as LocalHospitalIcon,
  SportsGymnastics as SportsGymnasticsIcon,
  Assessment as AssessmentIcon,
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Visibility as VisibilityIcon,
  CalendarToday as CalendarTodayIcon,
  Timer as TimerIcon,
  Star as StarIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import UniversalFilter, { type FilterField } from '../components/common/UniversalFilter';
import { useUniversalFilter, filterFunctions, sortFunctions } from '../hooks/useUniversalFilter';

// Mock data for demonstration
interface RehabilitationPlan {
  id: string;
  patientId: string;
  patientName: string;
  title: string;
  description?: string;
  startDate: string;
  endDate?: string;
  status: string;
  progress: number;
  exercises: RehabilitationExercise[];
  progressReports: RehabilitationProgress[];
}

interface RehabilitationExercise {
  id: string;
  name: string;
  description?: string;
  frequency: string;
  duration: string;
  instructions?: string;
}

interface RehabilitationProgress {
  id: string;
  reportDate: string;
  painLevel: number;
  notes?: string;
  progressPercentage: number;
  submittedByPatient: boolean;
}

const mockRehabPlans: RehabilitationPlan[] = [
  {
    id: '1',
    patientId: '1',
    patientName: 'John Smith',
    title: 'Post Knee Replacement Rehabilitation',
    description: 'Comprehensive rehabilitation program to restore knee function and mobility following total knee replacement surgery.',
    startDate: '2023-01-20',
    endDate: '2023-04-20',
    status: 'Active',
    progress: 65,
    exercises: [
      {
        id: '1',
        name: 'Straight Leg Raises',
        description: 'Strengthen quadriceps muscles',
        frequency: '3 times per day',
        duration: '10 repetitions',
        instructions: 'Lie on your back, keep one leg straight and the other bent. Raise the straight leg to the height of the bent knee.',
      },
      {
        id: '2',
        name: 'Knee Flexion Exercises',
        description: 'Improve knee bending ability',
        frequency: '2 times per day',
        duration: '15 repetitions',
        instructions: 'Sit on a chair, bend your knee as far as comfortable, hold for 5 seconds, then return to starting position.',
      },
      {
        id: '3',
        name: 'Walking',
        description: 'Improve overall mobility and strength',
        frequency: 'Daily',
        duration: '15-30 minutes',
        instructions: 'Walk on a flat surface using walking aids as needed. Gradually increase distance as tolerated.',
      },
    ],
    progressReports: [
      {
        id: '1',
        reportDate: '2023-02-01',
        painLevel: 6,
        notes: 'Patient reports moderate pain during exercises. Limited range of motion.',
        progressPercentage: 20,
        submittedByPatient: false,
      },
      {
        id: '2',
        reportDate: '2023-03-01',
        painLevel: 4,
        notes: 'Pain has decreased. Range of motion improving. Patient able to walk short distances without assistance.',
        progressPercentage: 45,
        submittedByPatient: false,
      },
      {
        id: '3',
        reportDate: '2023-04-01',
        painLevel: 2,
        notes: 'Significant improvement in mobility and strength. Patient reports minimal pain.',
        progressPercentage: 75,
        submittedByPatient: false,
      },
    ],
  },
  {
    id: '2',
    patientId: '2',
    patientName: 'Emily Johnson',
    title: 'Shoulder Rehabilitation Program',
    description: 'Rehabilitation program for rotator cuff injury and shoulder impingement.',
    startDate: '2023-02-15',
    endDate: '2023-05-15',
    status: 'Active',
    progress: 40,
    exercises: [
      {
        id: '4',
        name: 'Pendulum Exercise',
        description: 'Gentle shoulder mobility exercise',
        frequency: '3 times per day',
        duration: '1 minute each direction',
        instructions: 'Lean forward, let arm hang down, and gently swing in circular motions.',
      },
      {
        id: '5',
        name: 'Wall Slides',
        description: 'Improve shoulder mobility',
        frequency: '2 times per day',
        duration: '10 repetitions',
        instructions: 'Face wall with arms extended, slowly slide arms upward as high as comfortable.',
      },
    ],
    progressReports: [
      {
        id: '4',
        reportDate: '2023-03-15',
        painLevel: 5,
        notes: 'Limited range of motion. Pain during external rotation.',
        progressPercentage: 25,
        submittedByPatient: false,
      },
      {
        id: '5',
        reportDate: '2023-04-15',
        painLevel: 3,
        notes: 'Improved range of motion. Patient able to perform daily activities with less pain.',
        progressPercentage: 50,
        submittedByPatient: false,
      },
    ],
  },
];

// Mock patient data for the dropdown
interface Patient {
  id: string;
  name: string;
}

const mockPatients: Patient[] = [
  { id: '1', name: 'John Smith' },
  { id: '2', name: 'Emily Johnson' },
  { id: '3', name: 'Michael Brown' },
  { id: '4', name: 'Sarah Davis' },
  { id: '5', name: 'Robert Wilson' },
];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`rehab-tabpanel-${index}`}
      aria-labelledby={`rehab-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const Rehabilitation = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // State management
  const [rehabPlans, setRehabPlans] = useState<RehabilitationPlan[]>([]);
  const [filteredPlans, setFilteredPlans] = useState<RehabilitationPlan[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [tabValue, setTabValue] = useState(0);
  const [openPlanDialog, setOpenPlanDialog] = useState(false);
  const [openExerciseDialog, setOpenExerciseDialog] = useState(false);
  const [openProgressDialog, setOpenProgressDialog] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<RehabilitationPlan | null>(null);
  const [selectedExercise, setSelectedExercise] = useState<RehabilitationExercise | null>(null);
  const [statusFilter, setStatusFilter] = useState('');
  const [patientFilter, setPatientFilter] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);

  // Fetch rehabilitation plans and patients from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch rehabilitation plans
        const plans = await rehabilitationAPI.getRehabilitationPlans();

        // Fetch patients to get patient names
        const patientsData = await patientsAPI.getPatients();
        const patientMap = new Map(patientsData.map((p: any) => [p.id, `${p.first_name} ${p.last_name}`]));

        // Format rehabilitation plans
        const formattedPlans = plans.map((plan: any) => ({
          id: plan.id,
          patientId: plan.patient_id,
          patientName: patientMap.get(plan.patient_id) || 'Unknown Patient',
          title: plan.title,
          description: plan.description,
          startDate: plan.start_date,
          endDate: plan.end_date,
          status: plan.status,
          progress: plan.progress || 0,
          exercises: plan.exercises || [],
          progressReports: plan.progress_reports || []
        }));

        setRehabPlans(formattedPlans);
        setPatients(patientsData.map((p: any) => ({
          id: p.id,
          name: `${p.first_name} ${p.last_name}`
        })));

      } catch (err) {
        console.error('Error fetching rehabilitation data:', err);
        setError('Failed to load rehabilitation data. Please try again later.');
        // Fall back to mock data
        setRehabPlans(mockRehabPlans);
        setPatients(mockPatients);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    let result = rehabPlans;

    // Apply status filter
    if (statusFilter) {
      result = result.filter((plan) => plan.status === statusFilter);
    }

    // Apply patient filter
    if (patientFilter) {
      result = result.filter((plan) => plan.patientId === patientFilter);
    }

    setFilteredPlans(result);
  }, [rehabPlans, statusFilter, patientFilter]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
  };

  const handlePatientFilterChange = (event: SelectChangeEvent) => {
    setPatientFilter(event.target.value);
  };

  const handleAddPlan = () => {
    setSelectedPlan(null);
    setOpenPlanDialog(true);
  };

  const handleEditPlan = (plan: RehabilitationPlan) => {
    setSelectedPlan(plan);
    setOpenPlanDialog(true);
  };

  const handleDeletePlan = (id: string) => {
    // In a real application, you would make an API call to delete the plan
    // For now, we'll just filter the plan out of our local state
    setRehabPlans(rehabPlans.filter((plan) => plan.id !== id));
  };

  const handleAddExercise = (plan: RehabilitationPlan) => {
    setSelectedPlan(plan);
    setSelectedExercise(null);
    setOpenExerciseDialog(true);
  };

  const handleEditExercise = (plan: RehabilitationPlan, exercise: RehabilitationExercise) => {
    setSelectedPlan(plan);
    setSelectedExercise(exercise);
    setOpenExerciseDialog(true);
  };

  const handleAddProgress = (plan: RehabilitationPlan) => {
    setSelectedPlan(plan);
    setOpenProgressDialog(true);
  };

  const handleClosePlanDialog = () => {
    setOpenPlanDialog(false);
  };

  const handleCloseExerciseDialog = () => {
    setOpenExerciseDialog(false);
  };

  const handleCloseProgressDialog = () => {
    setOpenProgressDialog(false);
  };

  // Card expansion handlers
  const toggleCardExpansion = (planId: string) => {
    const newExpanded = new Set(expandedCards);
    if (newExpanded.has(planId)) {
      newExpanded.delete(planId);
    } else {
      newExpanded.add(planId);
    }
    setExpandedCards(newExpanded);
  };

  const isCardExpanded = (planId: string) => expandedCards.has(planId);

  // Get unique statuses for filter
  const statuses = Array.from(new Set(rehabPlans.map((plan) => plan.status)));

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Completed': return 'primary';
      case 'On Hold': return 'warning';
      case 'Cancelled': return 'error';
      default: return 'default';
    }
  };

  // Get progress color
  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'success';
    if (progress >= 60) return 'info';
    if (progress >= 40) return 'warning';
    return 'error';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Enhanced Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems={{ xs: 'flex-start', sm: 'center' }} spacing={2}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'primary.main' }}>
              Rehabilitation Center
            </Typography>
            <Typography variant="body1" sx={{ color: 'text.secondary', mb: 2 }}>
              Comprehensive rehabilitation management for orthopedic recovery
            </Typography>
            <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
              <Chip
                icon={<FitnessCenterIcon />}
                label={`${rehabPlans.filter(p => p.status === 'Active').length} Active Plans`}
                color="success"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<CheckCircleIcon />}
                label={`${rehabPlans.filter(p => p.status === 'Completed').length} Completed`}
                color="primary"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<AssessmentIcon />}
                label={`${rehabPlans.reduce((acc, plan) => acc + plan.progressReports.length, 0)} Reports`}
                color="info"
                variant="outlined"
                size="small"
              />
            </Stack>
          </Box>
          <Stack direction={{ xs: 'row', sm: 'row' }} spacing={1} sx={{ width: { xs: '100%', sm: 'auto' } }}>
            <Tooltip title="Refresh Data">
              <IconButton
                onClick={() => window.location.reload()}
                sx={{
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                  '&:hover': {
                    bgcolor: 'action.hover',
                  }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            {isMobile && (
              <Tooltip title="Toggle Filters">
                <IconButton
                  onClick={() => setShowFilters(!showFilters)}
                  sx={{
                    bgcolor: 'background.paper',
                    border: '1px solid',
                    borderColor: 'divider',
                    '&:hover': {
                      bgcolor: 'action.hover',
                    }
                  }}
                >
                  <FilterListIcon />
                </IconButton>
              </Tooltip>
            )}
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddPlan}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1.5,
                boxShadow: theme.shadows[3],
                '&:hover': {
                  boxShadow: theme.shadows[6],
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.2s ease-in-out',
                minWidth: { xs: 'auto', sm: 'auto' }
              }}
              fullWidth={isMobile}
            >
              {isMobile ? 'New Plan' : 'Create Rehabilitation Plan'}
            </Button>
          </Stack>
        </Stack>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Fade in={Boolean(error)}>
          <Alert
            severity="error"
            sx={{ mb: 3, borderRadius: 2 }}
            action={
              <Button color="inherit" size="small" onClick={() => setError(null)}>
                Dismiss
              </Button>
            }
          >
            {error}
          </Alert>
        </Fade>
      )}

      {/* Responsive Filters */}
      <Collapse in={!isMobile || showFilters}>
        <Card elevation={0} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
              <FilterListIcon color="primary" />
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Filters
              </Typography>
            </Stack>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    label="Status"
                    onChange={handleStatusFilterChange}
                    sx={{ borderRadius: 1 }}
                  >
                    <MenuItem value="">All Statuses</MenuItem>
                    {statuses.map((status) => (
                      <MenuItem key={status} value={status}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Chip
                            size="small"
                            label={status}
                            color={getStatusColor(status) as any}
                            variant="outlined"
                          />
                        </Stack>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Patient</InputLabel>
                  <Select
                    value={patientFilter}
                    label="Patient"
                    onChange={handlePatientFilterChange}
                    sx={{ borderRadius: 1 }}
                  >
                    <MenuItem value="">All Patients</MenuItem>
                    {patients.map((patient) => (
                      <MenuItem key={patient.id} value={patient.id}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Avatar sx={{ width: 24, height: 24, fontSize: '0.75rem' }}>
                            {patient.name.split(' ').map(n => n[0]).join('')}
                          </Avatar>
                          <Typography>{patient.name}</Typography>
                        </Stack>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Stack direction="row" spacing={1} alignItems="center" sx={{ height: '100%' }}>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      setStatusFilter('');
                      setPatientFilter('');
                    }}
                    sx={{ borderRadius: 1 }}
                  >
                    Clear Filters
                  </Button>
                  <Typography variant="body2" color="text.secondary">
                    {filteredPlans.length} of {rehabPlans.length} plans
                  </Typography>
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Collapse>

      {/* Responsive Tabs */}
      <Paper elevation={0} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="rehabilitation tabs"
          variant={isMobile ? 'fullWidth' : 'standard'}
          scrollButtons="auto"
          allowScrollButtonsMobile
          sx={{
            bgcolor: 'background.default',
            '& .MuiTab-root': {
              minHeight: { xs: 48, sm: 64 },
              fontSize: { xs: '0.875rem', sm: '1rem' },
              fontWeight: 600,
            }
          }}
        >
          <Tab
            icon={<FitnessCenterIcon />}
            label={isMobile ? 'Active' : 'Active Plans'}
            iconPosition="start"
            sx={{
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 0.5, sm: 1 }
            }}
          />
          <Tab
            icon={<CheckCircleIcon />}
            label={isMobile ? 'Completed' : 'Completed Plans'}
            iconPosition="start"
            sx={{
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 0.5, sm: 1 }
            }}
          />
          <Tab
            icon={<TimelineIcon />}
            label={isMobile ? 'All' : 'All Plans'}
            iconPosition="start"
            sx={{
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 0.5, sm: 1 }
            }}
          />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {renderRehabPlans('Active')}
        </TabPanel>
        <TabPanel value={tabValue} index={1}>
          {renderRehabPlans('Completed')}
        </TabPanel>
        <TabPanel value={tabValue} index={2}>
          {renderRehabPlans()}
        </TabPanel>
      </Paper>

      {/* Rehabilitation Plan Dialog */}
      <Dialog open={openPlanDialog} onClose={handleClosePlanDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedPlan ? 'Edit Rehabilitation Plan' : 'Create Rehabilitation Plan'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Patient</InputLabel>
                <Select
                  label="Patient"
                  defaultValue={selectedPlan?.patientId || ''}
                >
                  {patients.map((patient) => (
                    <MenuItem key={patient.id} value={patient.id}>
                      {patient.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Title"
                defaultValue={selectedPlan?.title || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Start Date"
                  defaultValue={selectedPlan ? new Date(selectedPlan.startDate) : new Date()}
                  sx={{ width: '100%' }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="End Date (Optional)"
                  defaultValue={selectedPlan?.endDate ? new Date(selectedPlan.endDate) : null}
                  sx={{ width: '100%' }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  label="Status"
                  defaultValue={selectedPlan?.status || 'Active'}
                >
                  <MenuItem value="Active">Active</MenuItem>
                  <MenuItem value="Completed">Completed</MenuItem>
                  <MenuItem value="On Hold">On Hold</MenuItem>
                  <MenuItem value="Cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                defaultValue={selectedPlan?.description || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePlanDialog}>Cancel</Button>
          <Button variant="contained" onClick={handleClosePlanDialog}>
            {selectedPlan ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Exercise Dialog */}
      <Dialog open={openExerciseDialog} onClose={handleCloseExerciseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedExercise ? 'Edit Exercise' : 'Add Exercise'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Exercise Name"
                defaultValue={selectedExercise?.name || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Frequency"
                defaultValue={selectedExercise?.frequency || ''}
                placeholder="e.g., 3 times per day"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Duration"
                defaultValue={selectedExercise?.duration || ''}
                placeholder="e.g., 10 repetitions"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={2}
                defaultValue={selectedExercise?.description || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Instructions"
                multiline
                rows={3}
                defaultValue={selectedExercise?.instructions || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseExerciseDialog}>Cancel</Button>
          <Button variant="contained" onClick={handleCloseExerciseDialog}>
            {selectedExercise ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Progress Report Dialog */}
      <Dialog open={openProgressDialog} onClose={handleCloseProgressDialog} maxWidth="md" fullWidth>
        <DialogTitle>Add Progress Report</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Report Date"
                  defaultValue={new Date()}
                  sx={{ width: '100%' }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Pain Level (1-10)</InputLabel>
                <Select
                  label="Pain Level (1-10)"
                  defaultValue="5"
                >
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((level) => (
                    <MenuItem key={level} value={level.toString()}>
                      {level}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Progress Percentage"
                type="number"
                defaultValue="0"
                InputProps={{ inputProps: { min: 0, max: 100 } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                defaultValue=""
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseProgressDialog}>Cancel</Button>
          <Button variant="contained" onClick={handleCloseProgressDialog}>
            Add Report
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button for Mobile */}
      {isMobile && (
        <Fab
          color="primary"
          aria-label="add rehabilitation plan"
          onClick={handleAddPlan}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            boxShadow: theme.shadows[6],
            '&:hover': {
              boxShadow: theme.shadows[12],
              transform: 'scale(1.1)',
            },
            transition: 'all 0.2s ease-in-out'
          }}
        >
          <AddIcon />
        </Fab>
      )}
    </Box>
  );

  function renderRehabPlans(statusFilter?: string) {
    const plans = statusFilter
      ? filteredPlans.filter((plan) => plan.status === statusFilter)
      : filteredPlans;

    if (plans.length === 0) {
      return (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          py: 8,
          px: 3
        }}>
          <FitnessCenterIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
          <Typography variant="h5" color="text.secondary" gutterBottom>
            No rehabilitation plans found
          </Typography>
          <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 3, maxWidth: 500 }}>
            {statusFilter
              ? `No ${statusFilter.toLowerCase()} rehabilitation plans match your current filters.`
              : 'Get started by creating your first rehabilitation plan for a patient.'
            }
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddPlan}
            sx={{ borderRadius: 2 }}
          >
            Create Rehabilitation Plan
          </Button>
        </Box>
      );
    }

    return (
      <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {plans.map((plan) => (
            <Grid item xs={12} lg={6} xl={4} key={plan.id}>
              <Zoom in timeout={300}>
                <Card
                  elevation={0}
                  sx={{
                    height: '100%',
                    borderRadius: 3,
                    border: '1px solid',
                    borderColor: 'divider',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      borderColor: 'primary.main',
                      boxShadow: theme.shadows[8],
                      transform: 'translateY(-4px)',
                    },
                    cursor: 'pointer',
                  }}
                  onClick={() => toggleCardExpansion(plan.id)}
                >
                  <CardContent sx={{ p: 3 }}>
                    {/* Header */}
                    <Stack direction="row" justifyContent="space-between" alignItems="flex-start" spacing={2} sx={{ mb: 2 }}>
                      <Stack direction="row" alignItems="center" spacing={1} sx={{ minWidth: 0, flex: 1 }}>
                        <Avatar sx={{ bgcolor: 'primary.main', width: 40, height: 40 }}>
                          {plan.patientName.split(' ').map(n => n[0]).join('')}
                        </Avatar>
                        <Box sx={{ minWidth: 0, flex: 1 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600, lineHeight: 1.2 }} noWrap>
                            {plan.patientName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" noWrap>
                            {plan.title}
                          </Typography>
                        </Box>
                      </Stack>
                      <Stack alignItems="flex-end" spacing={1}>
                        <Chip
                          size="small"
                          label={plan.status}
                          color={getStatusColor(plan.status) as any}
                          variant="filled"
                          sx={{ fontWeight: 600 }}
                        />
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleCardExpansion(plan.id);
                          }}
                        >
                          {isCardExpanded(plan.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                      </Stack>
                    </Stack>

                    {/* Progress Section */}
                    <Box sx={{ mb: 2 }}>
                      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          Progress
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {plan.progress}%
                        </Typography>
                      </Stack>
                      <LinearProgress
                        variant="determinate"
                        value={plan.progress}
                        color={getProgressColor(plan.progress) as any}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          bgcolor: alpha(theme.palette.grey[300], 0.3),
                        }}
                      />
                    </Box>

                    {/* Date Range */}
                    <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                      <CalendarTodayIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.secondary">
                        {new Date(plan.startDate).toLocaleDateString()} - {plan.endDate ? new Date(plan.endDate).toLocaleDateString() : 'Ongoing'}
                      </Typography>
                    </Stack>

                    {/* Quick Stats */}
                    <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                      <Stack direction="row" alignItems="center" spacing={0.5}>
                        <SportsGymnasticsIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                        <Typography variant="body2" color="text.secondary">
                          {plan.exercises.length} exercises
                        </Typography>
                      </Stack>
                      <Stack direction="row" alignItems="center" spacing={0.5}>
                        <AssessmentIcon sx={{ fontSize: 16, color: 'info.main' }} />
                        <Typography variant="body2" color="text.secondary">
                          {plan.progressReports.length} reports
                        </Typography>
                      </Stack>
                    </Stack>

                    {/* Description */}
                    {plan.description && (
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {plan.description.length > 100 && !isCardExpanded(plan.id)
                          ? `${plan.description.substring(0, 100)}...`
                          : plan.description
                        }
                      </Typography>
                    )}

                    {/* Expanded Content */}
                    <Collapse in={isCardExpanded(plan.id)}>
                      <Box sx={{ mt: 2 }}>
                        {/* Exercises Section */}
                        <Box sx={{ mb: 3 }}>
                          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
                              <SportsGymnasticsIcon sx={{ fontSize: 18 }} />
                              Exercises ({plan.exercises.length})
                            </Typography>
                            <Button
                              size="small"
                              startIcon={<AddIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAddExercise(plan);
                              }}
                              sx={{ borderRadius: 1 }}
                            >
                              Add
                            </Button>
                          </Stack>
                          {plan.exercises.length > 0 ? (
                            <List dense sx={{ bgcolor: 'background.default', borderRadius: 1 }}>
                              {plan.exercises.slice(0, 3).map((exercise) => (
                                <ListItem
                                  key={exercise.id}
                                  sx={{
                                    borderRadius: 1,
                                    mb: 0.5,
                                    '&:hover': { bgcolor: 'action.hover' }
                                  }}
                                >
                                  <ListItemIcon>
                                    <FitnessCenterIcon color="primary" sx={{ fontSize: 20 }} />
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={exercise.name}
                                    secondary={`${exercise.frequency} • ${exercise.duration}`}
                                    primaryTypographyProps={{ fontWeight: 500, fontSize: '0.875rem' }}
                                    secondaryTypographyProps={{ fontSize: '0.75rem' }}
                                  />
                                  <ListItemSecondaryAction>
                                    <IconButton
                                      edge="end"
                                      size="small"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleEditExercise(plan, exercise);
                                      }}
                                    >
                                      <EditIcon sx={{ fontSize: 16 }} />
                                    </IconButton>
                                  </ListItemSecondaryAction>
                                </ListItem>
                              ))}
                              {plan.exercises.length > 3 && (
                                <ListItem>
                                  <ListItemText
                                    primary={`+${plan.exercises.length - 3} more exercises`}
                                    primaryTypographyProps={{
                                      color: 'text.secondary',
                                      fontSize: '0.875rem',
                                      textAlign: 'center'
                                    }}
                                  />
                                </ListItem>
                              )}
                            </List>
                          ) : (
                            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                              No exercises added yet
                            </Typography>
                          )}
                        </Box>

                        {/* Progress Reports Section */}
                        <Box sx={{ mb: 2 }}>
                          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
                              <AssessmentIcon sx={{ fontSize: 18 }} />
                              Recent Progress ({plan.progressReports.length})
                            </Typography>
                            <Button
                              size="small"
                              startIcon={<AddIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAddProgress(plan);
                              }}
                              sx={{ borderRadius: 1 }}
                            >
                              Add
                            </Button>
                          </Stack>
                          {plan.progressReports.length > 0 ? (
                            <List dense sx={{ bgcolor: 'background.default', borderRadius: 1 }}>
                              {plan.progressReports
                                .sort((a, b) => new Date(b.reportDate).getTime() - new Date(a.reportDate).getTime())
                                .slice(0, 2)
                                .map((report) => (
                                  <ListItem
                                    key={report.id}
                                    sx={{
                                      borderRadius: 1,
                                      mb: 0.5,
                                      '&:hover': { bgcolor: 'action.hover' }
                                    }}
                                  >
                                    <ListItemIcon>
                                      <TrendingUpIcon color="info" sx={{ fontSize: 20 }} />
                                    </ListItemIcon>
                                    <ListItemText
                                      primary={`${new Date(report.reportDate).toLocaleDateString()} • Pain: ${report.painLevel}/10`}
                                      secondary={report.notes?.substring(0, 60) + (report.notes && report.notes.length > 60 ? '...' : '')}
                                      primaryTypographyProps={{ fontWeight: 500, fontSize: '0.875rem' }}
                                      secondaryTypographyProps={{ fontSize: '0.75rem' }}
                                    />
                                  </ListItem>
                                ))}
                            </List>
                          ) : (
                            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                              No progress reports yet
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </Collapse>
                  </CardContent>

                  {/* Card Actions */}
                  <CardActions sx={{ px: 3, pb: 3, pt: 0 }}>
                    <Stack direction="row" spacing={1} sx={{ width: '100%' }}>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditPlan(plan);
                        }}
                        sx={{ borderRadius: 1, flex: 1 }}
                      >
                        Edit
                      </Button>
                      <Button
                        size="small"
                        startIcon={<VisibilityIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle view details
                        }}
                        sx={{ borderRadius: 1, flex: 1 }}
                      >
                        View
                      </Button>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeletePlan(plan.id);
                        }}
                        sx={{ borderRadius: 1 }}
                      >
                        <DeleteIcon sx={{ fontSize: 18 }} />
                      </IconButton>
                    </Stack>
                  </CardActions>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }
};

export default Rehabilitation;
