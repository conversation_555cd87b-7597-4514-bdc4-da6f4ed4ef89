<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565c0;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="200" fill="url(#bg)" rx="100"/>
  <circle cx="100" cy="75" r="30" fill="white"/>
  <path d="M60 140C60 115 80 95 105 95H95C120 95 140 115 140 140V200H60V140Z" fill="white"/>
  <text x="100" y="165" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1976d2" text-anchor="middle">Dr<PERSON> <PERSON></text>
</svg>
