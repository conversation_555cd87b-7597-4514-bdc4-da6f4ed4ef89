from datetime import datetime
import os
import json
import io

# Optional imports for PDF generation
try:
    from reportlab.lib.pagesizes import letter
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    from PIL import Image as PILImage
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def generate_patient_report(patient, medical_images=None, appointments=None, rehabilitation_plans=None):
    """
    Generate a PDF report for a patient including their medical records, images, appointments, and rehabilitation plans.

    Args:
        patient: Patient model instance
        medical_images: List of MedicalImage model instances
        appointments: List of Appointment model instances
        rehabilitation_plans: List of RehabilitationPlan model instances

    Returns:
        bytes: PDF file as bytes
    """
    if not REPORTLAB_AVAILABLE:
        raise ImportError("ReportLab is not available. Please install it with: pip install reportlab")

    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter)
    styles = getSampleStyleSheet()

    # Create custom styles
    styles.add(ParagraphStyle(
        name='Title',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=12
    ))

    styles.add(ParagraphStyle(
        name='Heading2',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=10
    ))

    styles.add(ParagraphStyle(
        name='Normal',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6
    ))

    # Build the document content
    content = []

    # Title
    content.append(Paragraph(f"Patient Medical Report", styles['Title']))
    content.append(Paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M')}", styles['Normal']))
    content.append(Spacer(1, 0.25*inch))

    # Patient Information
    content.append(Paragraph("Patient Information", styles['Heading2']))

    patient_data = [
        ["Name", f"{patient.first_name} {patient.last_name}"],
        ["Date of Birth", patient.date_of_birth.strftime('%Y-%m-%d')],
        ["Gender", patient.gender],
        ["Contact", patient.contact_number],
        ["Email", patient.email or "N/A"],
        ["Address", patient.address or "N/A"]
    ]

    patient_table = Table(patient_data, colWidths=[2*inch, 4*inch])
    patient_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('BACKGROUND', (1, 0), (-1, -1), colors.white),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    content.append(patient_table)
    content.append(Spacer(1, 0.25*inch))

    # Medical History
    content.append(Paragraph("Medical History", styles['Heading2']))
    content.append(Paragraph(patient.medical_history or "No medical history recorded.", styles['Normal']))
    content.append(Spacer(1, 0.25*inch))

    # Allergies
    content.append(Paragraph("Allergies", styles['Heading2']))
    content.append(Paragraph(patient.allergies or "No allergies recorded.", styles['Normal']))
    content.append(Spacer(1, 0.25*inch))

    # Medical Images
    if medical_images and len(medical_images) > 0:
        content.append(Paragraph("Medical Images", styles['Heading2']))

        for image in medical_images:
            content.append(Paragraph(f"Type: {image.image_type} - Body Part: {image.body_part}", styles['Normal']))
            content.append(Paragraph(f"Date: {image.taken_date.strftime('%Y-%m-%d')}", styles['Normal']))

            if image.description:
                content.append(Paragraph(f"Description: {image.description}", styles['Normal']))

            # Add image if it's a supported format (not DICOM)
            if image.file_type.lower() in ['jpg', 'jpeg', 'png']:
                try:
                    img = PILImage.open(image.file_path)
                    img_width, img_height = img.size

                    # Resize if necessary to fit on page
                    max_width = 6 * inch
                    max_height = 4 * inch

                    if img_width > max_width or img_height > max_height:
                        ratio = min(max_width/img_width, max_height/img_height)
                        img_width = img_width * ratio
                        img_height = img_height * ratio

                    content.append(Image(image.file_path, width=img_width, height=img_height))
                except Exception as e:
                    content.append(Paragraph(f"Error displaying image: {str(e)}", styles['Normal']))
            else:
                content.append(Paragraph(f"Image format {image.file_type} cannot be displayed in PDF.", styles['Normal']))

            content.append(Spacer(1, 0.25*inch))

    # Appointments
    if appointments and len(appointments) > 0:
        content.append(Paragraph("Appointments", styles['Heading2']))

        appointment_data = [["Date", "Title", "Status", "Notes"]]

        for appointment in appointments:
            appointment_data.append([
                appointment.appointment_date.strftime('%Y-%m-%d %H:%M'),
                appointment.title,
                appointment.status,
                appointment.notes or ""
            ])

        appointment_table = Table(appointment_data, colWidths=[1.5*inch, 1.5*inch, 1*inch, 2*inch])
        appointment_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        content.append(appointment_table)
        content.append(Spacer(1, 0.25*inch))

    # Rehabilitation Plans
    if rehabilitation_plans and len(rehabilitation_plans) > 0:
        content.append(Paragraph("Rehabilitation Plans", styles['Heading2']))

        for plan in rehabilitation_plans:
            content.append(Paragraph(f"Plan: {plan.title}", styles['Normal']))
            content.append(Paragraph(f"Status: {plan.status}", styles['Normal']))
            content.append(Paragraph(f"Start Date: {plan.start_date.strftime('%Y-%m-%d')}", styles['Normal']))

            if plan.end_date:
                content.append(Paragraph(f"End Date: {plan.end_date.strftime('%Y-%m-%d')}", styles['Normal']))

            if plan.description:
                content.append(Paragraph(f"Description: {plan.description}", styles['Normal']))

            # Add exercises if available
            if hasattr(plan, 'exercises') and plan.exercises:
                content.append(Paragraph("Exercises:", styles['Normal']))

                exercise_data = [["Name", "Frequency", "Duration"]]

                for exercise in plan.exercises:
                    exercise_data.append([
                        exercise.name,
                        exercise.frequency,
                        exercise.duration
                    ])

                exercise_table = Table(exercise_data, colWidths=[2*inch, 2*inch, 2*inch])
                exercise_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                content.append(exercise_table)

            content.append(Spacer(1, 0.25*inch))

    # Build the PDF
    doc.build(content)

    # Get the PDF data
    pdf_data = buffer.getvalue()
    buffer.close()

    return pdf_data
