import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { dashboardAPI, appointmentsAPI } from '../services/api';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardHeader,
  List,
  ListItem,
  ListItemText,
  Divider,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  Stack,
  useTheme,
  alpha,
} from '@mui/material';
import {
  People as PeopleIcon,
  Event as EventIcon,
  Image as ImageIcon,
  FitnessCenter as FitnessCenterIcon,
  ArrowForward as ArrowForwardIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  AccessTime as AccessTimeIcon,
  LocalHospital as LocalHospitalIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';

// Types for dashboard data
interface DashboardStats {
  totalPatients: number;
  appointmentsToday: number;
  pendingImages: number;
  activeRehabPlans: number;
}

interface Appointment {
  id: string | number;
  patientName: string;
  time: string;
  reason: string;
}

interface Patient {
  id: string | number;
  name: string;
  age: number;
  condition: string;
}

const Dashboard = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [upcomingAppointments, setUpcomingAppointments] = useState<Appointment[]>([]);
  const [recentPatients, setRecentPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard data from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch dashboard statistics
        const stats = await dashboardAPI.getStats();
        setStats(stats);

        // Fetch today's appointments
        const todaysAppointments = await appointmentsAPI.getTodaysAppointments();
        const formattedAppointments = todaysAppointments.map((apt: any) => ({
          id: apt.id,
          patientName: apt.patient?.first_name && apt.patient?.last_name
            ? `${apt.patient.first_name} ${apt.patient.last_name}`
            : 'Unknown Patient',
          time: new Date(apt.appointment_date).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
          }),
          reason: apt.reason || 'General consultation'
        }));
        setUpcomingAppointments(formattedAppointments);

        // Fetch recent patients
        const recentPatients = await dashboardAPI.getRecentPatients(5);
        setRecentPatients(recentPatients);

      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  return (
    <Box>
      {/* Enhanced Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            opacity: 0.3,
          }
        }}
      >
        <Box sx={{ position: 'relative', zIndex: 1 }}>
          <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems={{ xs: 'flex-start', sm: 'center' }} spacing={2}>
            <Box>
              <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                Welcome Back, Dr. Sharma
              </Typography>
              <Typography variant="h6" sx={{ opacity: 0.9, fontWeight: 500 }}>
                Additional Professor, Department of Orthopaedics
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.8, mt: 0.5 }}>
                PGIMER Chandigarh • {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Typography>
            </Box>
            <Stack direction="row" spacing={2}>
              <Button
                variant="outlined"
                onClick={() => window.location.reload()}
                startIcon={<RefreshIcon />}
                sx={{
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: 'rgba(255, 255, 255, 0.1)',
                  }
                }}
              >
                Refresh
              </Button>
            </Stack>
          </Stack>
        </Box>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3, borderRadius: 1 }}
          action={
            <Button color="inherit" size="small" onClick={() => window.location.reload()}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Enhanced Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={0}
            sx={{
              p: 3,
              height: '100%',
              borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider',
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: theme.shadows[8],
                borderColor: 'primary.main',
              }
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  width: 56,
                  height: 56,
                  boxShadow: theme.shadows[3]
                }}
              >
                <PeopleIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Box sx={{ flex: 1 }}>
                {loading ? (
                  <CircularProgress size={24} sx={{ color: 'primary.main' }} />
                ) : (
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 0.5 }}>
                    {stats?.totalPatients || 0}
                  </Typography>
                )}
                <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  Total Patients
                </Typography>
                <Stack direction="row" alignItems="center" spacing={0.5} sx={{ mt: 0.5 }}>
                  <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main' }} />
                  <Typography variant="caption" sx={{ color: 'success.main', fontWeight: 500 }}>
                    +12% this month
                  </Typography>
                </Stack>
              </Box>
            </Stack>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={0}
            sx={{
              p: 3,
              height: '100%',
              borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider',
              background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.success.main, 0.05)} 100%)`,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: theme.shadows[8],
                borderColor: 'success.main',
              }
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                sx={{
                  bgcolor: 'success.main',
                  width: 56,
                  height: 56,
                  boxShadow: theme.shadows[3]
                }}
              >
                <EventIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Box sx={{ flex: 1 }}>
                {loading ? (
                  <CircularProgress size={24} sx={{ color: 'success.main' }} />
                ) : (
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main', mb: 0.5 }}>
                    {stats?.appointmentsToday || 0}
                  </Typography>
                )}
                <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  Today's Appointments
                </Typography>
                <Stack direction="row" alignItems="center" spacing={0.5} sx={{ mt: 0.5 }}>
                  <AccessTimeIcon sx={{ fontSize: 16, color: 'info.main' }} />
                  <Typography variant="caption" sx={{ color: 'info.main', fontWeight: 500 }}>
                    Next at 2:30 PM
                  </Typography>
                </Stack>
              </Box>
            </Stack>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={0}
            sx={{
              p: 3,
              height: '100%',
              borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider',
              background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)} 0%, ${alpha(theme.palette.warning.main, 0.05)} 100%)`,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: theme.shadows[8],
                borderColor: 'warning.main',
              }
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                sx={{
                  bgcolor: 'warning.main',
                  width: 56,
                  height: 56,
                  boxShadow: theme.shadows[3]
                }}
              >
                <ImageIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Box sx={{ flex: 1 }}>
                {loading ? (
                  <CircularProgress size={24} sx={{ color: 'warning.main' }} />
                ) : (
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main', mb: 0.5 }}>
                    {stats?.pendingImages || 0}
                  </Typography>
                )}
                <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  Pending Images
                </Typography>
                <Stack direction="row" alignItems="center" spacing={0.5} sx={{ mt: 0.5 }}>
                  <LocalHospitalIcon sx={{ fontSize: 16, color: 'warning.main' }} />
                  <Typography variant="caption" sx={{ color: 'warning.main', fontWeight: 500 }}>
                    Awaiting review
                  </Typography>
                </Stack>
              </Box>
            </Stack>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={0}
            sx={{
              p: 3,
              height: '100%',
              borderRadius: 3,
              border: '1px solid',
              borderColor: 'divider',
              background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: theme.shadows[8],
                borderColor: 'secondary.main',
              }
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                sx={{
                  bgcolor: 'secondary.main',
                  width: 56,
                  height: 56,
                  boxShadow: theme.shadows[3]
                }}
              >
                <FitnessCenterIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Box sx={{ flex: 1 }}>
                {loading ? (
                  <CircularProgress size={24} sx={{ color: 'secondary.main' }} />
                ) : (
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main', mb: 0.5 }}>
                    {stats?.activeRehabPlans || 0}
                  </Typography>
                )}
                <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  Active Rehab Plans
                </Typography>
                <Stack direction="row" alignItems="center" spacing={0.5} sx={{ mt: 0.5 }}>
                  <AssignmentIcon sx={{ fontSize: 16, color: 'secondary.main' }} />
                  <Typography variant="caption" sx={{ color: 'secondary.main', fontWeight: 500 }}>
                    In progress
                  </Typography>
                </Stack>
              </Box>
            </Stack>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Slide direction="up" in={true} timeout={800}>
        <ResponsiveGrid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          {/* Upcoming Appointments */}
          <ResponsiveGridItem xs={12} md={6}>
            <ResponsiveCard
              title={
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Today's Appointments
                </Typography>
              }
              action={
                <Button
                  color="primary"
                  onClick={() => navigate('/appointments')}
                  endIcon={<ArrowForwardIcon />}
                  size={isMobile ? 'small' : 'medium'}
                  sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  View All
                </Button>
              }
              fullHeight={true}
              loading={loading}
              sx={{
                borderRadius: { xs: 2, sm: 3 }
              }}
            >
              <Divider sx={{ mx: { xs: 1, sm: 2 }, my: 1.5 }} />
              {upcomingAppointments.length > 0 ? (
                <List disablePadding>
                  {upcomingAppointments.map((appointment) => (
                    <div key={appointment.id}>
                      <ListItem sx={{ px: { xs: 1, sm: 2 }, py: { xs: 1, sm: 1.5 } }}>
                        <ListItemText
                          primary={
                            <Typography
                              variant={isMobile ? 'body1' : 'subtitle1'}
                              sx={{
                                fontWeight: 500,
                                fontSize: { xs: '0.875rem', sm: '1rem' }
                              }}
                            >
                              {appointment.patientName}
                            </Typography>
                          }
                          secondary={
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                fontSize: { xs: '0.75rem', sm: '0.875rem' }
                              }}
                            >
                              {appointment.time} - {appointment.reason}
                            </Typography>
                          }
                        />
                      </ListItem>
                      <Divider component="li" />
                    </div>
                  ))}
                </List>
              ) : (
                <Box sx={{
                  py: { xs: 3, sm: 4 },
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center'
                }}>
                  <EventIcon sx={{
                    fontSize: { xs: 40, sm: 48 },
                    color: 'text.disabled',
                    mb: 1
                  }} />
                  <Typography
                    variant={isMobile ? 'body1' : 'subtitle1'}
                    color="text.secondary"
                    gutterBottom
                    sx={{
                      fontSize: { xs: '0.875rem', sm: '1rem' }
                    }}
                  >
                    No appointments scheduled for today
                  </Typography>
                  <Button
                    variant="outlined"
                    size={isMobile ? 'small' : 'medium'}
                    onClick={() => navigate('/appointments')}
                    sx={{
                      mt: 1,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}
                  >
                    Schedule Appointment
                  </Button>
                </Box>
              )}
            </ResponsiveCard>
          </ResponsiveGridItem>

          {/* Recent Patients */}
          <ResponsiveGridItem xs={12} md={6}>
            <ResponsiveCard
              title={
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Recent Patients
                </Typography>
              }
              action={
                <Button
                  color="primary"
                  onClick={() => navigate('/patients')}
                  endIcon={<ArrowForwardIcon />}
                  size={isMobile ? 'small' : 'medium'}
                  sx={{
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  View All
                </Button>
              }
              fullHeight={true}
              loading={loading}
              sx={{
                borderRadius: { xs: 2, sm: 3 }
              }}
            >
              <Divider sx={{ mx: { xs: 1, sm: 2 }, my: 1.5 }} />
              {recentPatients.length > 0 ? (
                <List disablePadding>
                  {recentPatients.map((patient) => (
                    <div key={patient.id}>
                      <ListItem
                        button
                        onClick={() => navigate(`/patients/${patient.id}`)}
                        sx={{
                          px: { xs: 1, sm: 2 },
                          py: { xs: 1, sm: 1.5 },
                          borderRadius: 1,
                          '&:hover': {
                            bgcolor: 'action.hover',
                          }
                        }}
                      >
                        <ListItemText
                          primary={
                            <Typography
                              variant={isMobile ? 'body1' : 'subtitle1'}
                              sx={{
                                fontWeight: 500,
                                fontSize: { xs: '0.875rem', sm: '1rem' }
                              }}
                            >
                              {patient.name}
                            </Typography>
                          }
                          secondary={
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                fontSize: { xs: '0.75rem', sm: '0.875rem' }
                              }}
                            >
                              {patient.age} years - {patient.condition}
                            </Typography>
                          }
                        />
                      </ListItem>
                      <Divider component="li" />
                    </div>
                  ))}
                </List>
              ) : (
                <Box sx={{
                  py: { xs: 3, sm: 4 },
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center'
                }}>
                  <PeopleIcon sx={{
                    fontSize: { xs: 40, sm: 48 },
                    color: 'text.disabled',
                    mb: 1
                  }} />
                  <Typography
                    variant={isMobile ? 'body1' : 'subtitle1'}
                    color="text.secondary"
                    gutterBottom
                    sx={{
                      fontSize: { xs: '0.875rem', sm: '1rem' }
                    }}
                  >
                    No patients in the system yet
                  </Typography>
                  <Button
                    variant="outlined"
                    size={isMobile ? 'small' : 'medium'}
                    onClick={() => navigate('/patients')}
                    sx={{
                      mt: 1,
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}
                  >
                    Add Patient
                  </Button>
                </Box>
              )}
            </ResponsiveCard>
          </ResponsiveGridItem>
        </ResponsiveGrid>
      </Slide>
    </Box>
  );
};

export default Dashboard;
