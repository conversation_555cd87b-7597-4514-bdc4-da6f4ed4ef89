/* Accessibility improvements for the application */

/* Ensure focus is visible for keyboard navigation */
*:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Improve focus visibility for buttons */
button:focus-visible,
.MuiButton-root:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Improve focus visibility for form elements */
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
.MuiTextField-root input:focus-visible,
.MuiSelect-root:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Ensure modal content is properly focused */
.MuiDialog-root[aria-hidden="false"] {
  z-index: 1300;
}

/* Improve screen reader experience */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Ensure proper contrast for disabled elements */
.MuiButton-root:disabled,
.MuiTextField-root .Mui-disabled {
  opacity: 0.6;
}

/* Improve focus management for modals */
.MuiModal-root {
  isolation: isolate;
}

/* Ensure proper focus trapping */
.MuiDialog-container {
  outline: none;
}

.MuiDialog-paper {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .MuiButton-root {
    border: 1px solid currentColor;
  }
  
  .MuiCard-root,
  .MuiPaper-root {
    border: 1px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .MuiDialog-root .MuiDialog-paper {
    transition: none;
  }
  
  .MuiCircularProgress-root {
    animation: none;
  }
}

/* Ensure proper color contrast */
.MuiAlert-standardError {
  background-color: #fdeded;
  color: #5f2120;
}

.MuiAlert-standardWarning {
  background-color: #fff4e5;
  color: #663c00;
}

.MuiAlert-standardInfo {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.MuiAlert-standardSuccess {
  background-color: #e8f5e8;
  color: #2e7d32;
}
