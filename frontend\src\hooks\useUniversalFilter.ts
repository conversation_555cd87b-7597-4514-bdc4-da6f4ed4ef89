import { useState, useEffect, useMemo } from 'react';

export interface FilterConfig {
  key: string;
  defaultValue: any;
  filterFunction: (item: any, value: any) => boolean;
}

export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
  sortFunction?: (a: any, b: any) => number;
}

export interface UseUniversalFilterProps<T> {
  data: T[];
  searchFields?: string[];
  filterConfigs?: FilterConfig[];
  defaultSort?: string;
  sortConfigs?: Record<string, SortConfig>;
}

export interface UseUniversalFilterReturn<T> {
  // Data
  filteredData: T[];
  
  // Search
  searchValue: string;
  setSearchValue: (value: string) => void;
  
  // Filters
  filterValues: Record<string, any>;
  setFilterValue: (key: string, value: any) => void;
  clearFilters: () => void;
  
  // Sort
  sortValue: string;
  setSortValue: (value: string) => void;
  
  // Counts
  totalCount: number;
  filteredCount: number;
  
  // Reset
  resetAll: () => void;
}

export function useUniversalFilter<T>({
  data,
  searchFields = [],
  filterConfigs = [],
  defaultSort = '',
  sortConfigs = {},
}: UseUniversalFilterProps<T>): UseUniversalFilterReturn<T> {
  
  const [searchValue, setSearchValue] = useState('');
  const [filterValues, setFilterValues] = useState<Record<string, any>>(() => {
    const initial: Record<string, any> = {};
    filterConfigs.forEach(config => {
      initial[config.key] = config.defaultValue;
    });
    return initial;
  });
  const [sortValue, setSortValue] = useState(defaultSort);

  const setFilterValue = (key: string, value: any) => {
    setFilterValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    const clearedFilters: Record<string, any> = {};
    filterConfigs.forEach(config => {
      clearedFilters[config.key] = config.defaultValue;
    });
    setFilterValues(clearedFilters);
  };

  const resetAll = () => {
    setSearchValue('');
    clearFilters();
    setSortValue(defaultSort);
  };

  const filteredData = useMemo(() => {
    let result = [...data];

    // Apply search filter
    if (searchValue && searchFields.length > 0) {
      const lowerCaseSearch = searchValue.toLowerCase();
      result = result.filter(item => {
        return searchFields.some(field => {
          const fieldValue = getNestedValue(item, field);
          return fieldValue && 
                 fieldValue.toString().toLowerCase().includes(lowerCaseSearch);
        });
      });
    }

    // Apply custom filters
    filterConfigs.forEach(config => {
      const filterValue = filterValues[config.key];
      if (filterValue !== config.defaultValue && 
          filterValue !== '' && 
          filterValue !== null && 
          filterValue !== undefined &&
          (Array.isArray(filterValue) ? filterValue.length > 0 : true)) {
        result = result.filter(item => config.filterFunction(item, filterValue));
      }
    });

    // Apply sorting
    if (sortValue && sortConfigs[sortValue]) {
      const sortConfig = sortConfigs[sortValue];
      result.sort((a, b) => {
        if (sortConfig.sortFunction) {
          const sortResult = sortConfig.sortFunction(a, b);
          return sortConfig.direction === 'desc' ? -sortResult : sortResult;
        } else {
          // Default string/number sorting
          const aValue = getNestedValue(a, sortConfig.key);
          const bValue = getNestedValue(b, sortConfig.key);
          
          if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
          return 0;
        }
      });
    }

    return result;
  }, [data, searchValue, filterValues, sortValue, searchFields, filterConfigs, sortConfigs]);

  return {
    filteredData,
    searchValue,
    setSearchValue,
    filterValues,
    setFilterValue,
    clearFilters,
    sortValue,
    setSortValue,
    totalCount: data.length,
    filteredCount: filteredData.length,
    resetAll,
  };
}

// Helper function to get nested object values
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : '';
  }, obj);
}

// Predefined filter functions for common use cases
export const filterFunctions = {
  exact: (item: any, value: any, field: string) => {
    return getNestedValue(item, field) === value;
  },
  
  contains: (item: any, value: any, field: string) => {
    const fieldValue = getNestedValue(item, field);
    return fieldValue && fieldValue.toString().toLowerCase().includes(value.toLowerCase());
  },
  
  dateRange: (item: any, value: { start: string; end: string }, field: string) => {
    const itemDate = new Date(getNestedValue(item, field));
    const startDate = new Date(value.start);
    const endDate = new Date(value.end);
    return itemDate >= startDate && itemDate <= endDate;
  },
  
  multiSelect: (item: any, values: any[], field: string) => {
    const fieldValue = getNestedValue(item, field);
    return values.includes(fieldValue);
  },
  
  numberRange: (item: any, value: { min: number; max: number }, field: string) => {
    const fieldValue = Number(getNestedValue(item, field));
    return fieldValue >= value.min && fieldValue <= value.max;
  },
  
  boolean: (item: any, value: boolean, field: string) => {
    return Boolean(getNestedValue(item, field)) === value;
  }
};

// Predefined sort functions
export const sortFunctions = {
  string: (a: any, b: any, field: string) => {
    const aValue = getNestedValue(a, field) || '';
    const bValue = getNestedValue(b, field) || '';
    return aValue.localeCompare(bValue);
  },
  
  number: (a: any, b: any, field: string) => {
    const aValue = Number(getNestedValue(a, field)) || 0;
    const bValue = Number(getNestedValue(b, field)) || 0;
    return aValue - bValue;
  },
  
  date: (a: any, b: any, field: string) => {
    const aValue = new Date(getNestedValue(a, field)).getTime();
    const bValue = new Date(getNestedValue(b, field)).getTime();
    return aValue - bValue;
  }
};
