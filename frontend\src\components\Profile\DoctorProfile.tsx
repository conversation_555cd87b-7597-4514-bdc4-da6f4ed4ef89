import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Avatar,
  Typography,
  Chip,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  School as SchoolIcon,
  Work as WorkIcon,
  Star as StarIcon,
} from '@mui/icons-material';

const DoctorProfile: React.FC = () => {
  const doctorInfo = {
    name: 'Dr. <PERSON><PERSON>',
    title: 'Additional Professor',
    department: 'Department of Orthopaedics',
    institution: 'PGIMER Chandigarh',
    location: 'Chandigarh, India',
    email: '<EMAIL>',
    phone: '+91-172-2755555',
    specializations: [
      'Joint Replacement Surgery',
      'Arthroscopic Surgery',
      'Trauma Surgery',
      'Spine Surgery',
      'Sports Medicine',
      'Pediatric Orthopaedics'
    ],
    qualifications: [
      'MBBS',
      'MS (Orthopaedics)',
      'DNB (Orthopaedics)',
      'Fellowship in Joint Replacement'
    ],
    experience: '15+ Years',
    achievements: [
      'Published 50+ research papers in international journals',
      'Performed 2000+ successful surgeries',
      'Member of Indian Orthopaedic Association',
      'Recipient of Excellence in Teaching Award'
    ]
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Card elevation={3} sx={{ mb: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
              <Avatar
                src="/dr-sharma-profile.svg"
                alt={doctorInfo.name}
                sx={{
                  width: 200,
                  height: 200,
                  mx: 'auto',
                  mb: 2,
                  border: '4px solid',
                  borderColor: 'primary.main',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                  bgcolor: 'primary.main',
                  fontSize: '3rem',
                  fontWeight: 'bold'
                }}
              >
                DS
              </Avatar>
              <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: 'primary.main' }}>
                {doctorInfo.name}
              </Typography>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                {doctorInfo.title}
              </Typography>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                {doctorInfo.department}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500, color: 'primary.main' }}>
                {doctorInfo.institution}
              </Typography>
            </Grid>

            <Grid item xs={12} md={8}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                  Contact Information
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText primary={doctorInfo.email} />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText primary={doctorInfo.phone} />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <LocationIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText primary={doctorInfo.location} />
                  </ListItem>
                </List>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                  Experience
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <WorkIcon color="primary" />
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {doctorInfo.experience}
                  </Typography>
                </Box>
              </Box>

              <Box>
                <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                  Qualifications
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {doctorInfo.qualifications.map((qualification, index) => (
                    <Chip
                      key={index}
                      label={qualification}
                      color="primary"
                      variant="outlined"
                      icon={<SchoolIcon />}
                    />
                  ))}
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                Specializations
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {doctorInfo.specializations.map((specialization, index) => (
                  <Chip
                    key={index}
                    label={specialization}
                    color="secondary"
                    variant="filled"
                    size="small"
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                Key Achievements
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <List dense>
                {doctorInfo.achievements.map((achievement, index) => (
                  <ListItem key={index} sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      <StarIcon color="secondary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary={achievement}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DoctorProfile;
