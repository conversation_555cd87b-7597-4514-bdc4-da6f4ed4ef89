#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a sample DICOM file for testing
"""
import os
import numpy as np
from datetime import datetime

def create_sample_dicom():
    """Create a simple sample DICOM file"""
    try:
        import pydicom
        from pydicom.dataset import Dataset, FileDataset
        from pydicom.uid import ExplicitVRLittleEndian
        import tempfile
    except ImportError:
        print("pydicom not available. Creating a mock DICOM file instead.")
        create_mock_dicom()
        return

    print("Creating sample DICOM file...")

    # Create a simple 512x512 image with some patterns
    image_size = 512
    image_data = np.zeros((image_size, image_size), dtype=np.uint16)
    
    # Add some patterns to make it look like a medical image
    # Create a circular pattern (like a joint)
    center = image_size // 2
    y, x = np.ogrid[:image_size, :image_size]
    
    # Outer circle (bone)
    outer_mask = (x - center)**2 + (y - center)**2 <= (center - 50)**2
    image_data[outer_mask] = 3000
    
    # Inner circle (joint space)
    inner_mask = (x - center)**2 + (y - center)**2 <= (center - 100)**2
    image_data[inner_mask] = 1000
    
    # Add some noise for realism
    noise = np.random.normal(0, 100, (image_size, image_size))
    image_data = np.clip(image_data + noise, 0, 4095).astype(np.uint16)

    # Create the DICOM dataset
    file_meta = Dataset()
    file_meta.MediaStorageSOPClassUID = '1.2.840.10008.*******.1.2'  # CT Image Storage
    file_meta.MediaStorageSOPInstanceUID = '*******.*******.9.10'
    file_meta.ImplementationClassUID = '*******.*******.9.10'
    file_meta.TransferSyntaxUID = ExplicitVRLittleEndian

    # Create the main dataset
    ds = FileDataset("sample_knee_ct.dcm", {}, file_meta=file_meta, preamble=b"\0" * 128)

    # Add patient information
    ds.PatientName = "Test^Patient"
    ds.PatientID = "TEST001"
    ds.PatientBirthDate = "19800101"
    ds.PatientSex = "M"

    # Add study information
    ds.StudyDate = datetime.now().strftime("%Y%m%d")
    ds.StudyTime = datetime.now().strftime("%H%M%S")
    ds.StudyInstanceUID = "*******.*******.9.10.11"
    ds.StudyDescription = "Knee CT Scan"

    # Add series information
    ds.SeriesInstanceUID = "*******.*******.**********"
    ds.SeriesNumber = 1
    ds.SeriesDescription = "Axial CT"
    ds.Modality = "CT"

    # Add image information
    ds.SOPInstanceUID = "*******.*******.**********.13"
    ds.SOPClassUID = '1.2.840.10008.*******.1.2'
    ds.InstanceNumber = 1

    # Add image data
    ds.SamplesPerPixel = 1
    ds.PhotometricInterpretation = "MONOCHROME2"
    ds.Rows = image_size
    ds.Columns = image_size
    ds.BitsAllocated = 16
    ds.BitsStored = 12
    ds.HighBit = 11
    ds.PixelRepresentation = 0
    ds.PixelData = image_data.tobytes()

    # Add window/level information for proper display
    ds.WindowCenter = 2000
    ds.WindowWidth = 4000
    ds.RescaleIntercept = 0
    ds.RescaleSlope = 1

    # Save the file
    os.makedirs('test_images', exist_ok=True)
    filename = 'test_images/sample_knee_ct.dcm'
    ds.save_as(filename)
    
    print(f"Sample DICOM file created: {filename}")
    print(f"File size: {os.path.getsize(filename)} bytes")
    return filename

def create_mock_dicom():
    """Create a mock DICOM file when pydicom is not available"""
    print("Creating mock DICOM file...")
    
    # Create a simple binary file with DICOM-like header
    os.makedirs('test_images', exist_ok=True)
    filename = 'test_images/sample_knee_ct.dcm'
    
    # DICOM files start with a 128-byte preamble followed by "DICM"
    with open(filename, 'wb') as f:
        # Write preamble (128 null bytes)
        f.write(b'\x00' * 128)
        # Write DICOM prefix
        f.write(b'DICM')
        # Write some mock data
        f.write(b'Mock DICOM file for testing purposes' * 100)
    
    print(f"Mock DICOM file created: {filename}")
    print(f"File size: {os.path.getsize(filename)} bytes")
    return filename

if __name__ == "__main__":
    create_sample_dicom()
