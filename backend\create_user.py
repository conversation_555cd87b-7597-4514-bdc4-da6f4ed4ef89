#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a user for <PERSON><PERSON>
"""
import sys
import os
sys.path.append(os.getcwd())

from app.database.database import SessionLocal
from app.models.models import User
from app.auth.auth import get_password_hash

def create_user():
    db = SessionLocal()
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("User already exists!")
            return
        
        # Create new user
        hashed_password = get_password_hash("password")
        
        new_user = User(
            email="<EMAIL>",
            full_name="Dr<PERSON>",
            hashed_password=hashed_password,
            is_active=True
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        print(f"User created successfully!")
        print(f"Email: {new_user.email}")
        print(f"Name: {new_user.full_name}")
        print(f"ID: {new_user.id}")
        
    except Exception as e:
        print(f"Error creating user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_user()
