#!/usr/bin/env python3
"""
Database initialization script for the Orthopedic EHR System
This script creates the database tables and adds a default user for development
"""

import sys
import os
from sqlalchemy.orm import Session

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import engine, SessionLocal
from app.models.models import Base, User
from app.auth.auth import get_password_hash

def create_tables():
    """Create all database tables"""
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("✓ Database tables created successfully")

def create_default_user():
    """Create a default user for development"""
    db = SessionLocal()
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("✓ Default user already exists")
            return

        # Create default user
        hashed_password = get_password_hash("password")
        default_user = User(
            email="<EMAIL>",
            hashed_password=hashed_password,
            full_name="<PERSON><PERSON>",
            is_active=True
        )

        db.add(default_user)
        db.commit()
        db.refresh(default_user)

        print("✓ Default user created successfully")
        print("  Email: <EMAIL>")
        print("  Password: password")

    except Exception as e:
        print(f"✗ Error creating default user: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """Main initialization function"""
    print("Initializing Orthopedic EHR Database...")
    print("=" * 50)

    try:
        # Create tables
        create_tables()

        # Create default user
        create_default_user()

        print("=" * 50)
        print("✓ Database initialization completed successfully!")
        print("\nYou can now start the server with: python main.py")

    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
