from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import os
import shutil
import uuid

# Optional imports for medical image processing
try:
    import pydicom
    PYDICOM_AVAILABLE = True
except ImportError:
    PYDICOM_AVAILABLE = False

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from app.database.database import get_db
from app.models.models import MedicalImage, Patient
from app.schemas.schemas import MedicalImageCreate, MedicalImageUpdate, MedicalImageResponse
from app.auth.auth import get_current_active_user

router = APIRouter()

# Create directory for storing medical images if it doesn't exist
UPLOAD_DIRECTORY = "uploads/medical_images"
os.makedirs(UPLOAD_DIRECTORY, exist_ok=True)

@router.post("/upload", response_model=MedicalImageResponse)
async def upload_medical_image(
    patient_id: str = Form(...),
    image_type: str = Form(...),
    body_part: str = Form(...),
    description: Optional[str] = Form(None),
    taken_date: datetime = Form(...),
    annotations: Optional[str] = Form(None),
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    # Check if patient exists
    db_patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not db_patient:
        raise HTTPException(status_code=404, detail="Patient not found")

    # Generate unique filename
    file_extension = os.path.splitext(file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(UPLOAD_DIRECTORY, unique_filename)

    # Save file
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)

    # Determine file type
    file_type = file_extension.lstrip('.').upper()

    # If it's a DICOM file, try to extract additional metadata
    if file_extension.lower() == '.dcm' and PYDICOM_AVAILABLE:
        try:
            dicom_data = pydicom.dcmread(file_path)
            # You could extract additional metadata here if needed
            print(f"DICOM file processed: {dicom_data.get('PatientName', 'Unknown')}")
        except Exception as e:
            print(f"Error reading DICOM file: {e}")

    # Create database entry
    db_medical_image = MedicalImage(
        patient_id=patient_id,
        image_type=image_type,
        body_part=body_part,
        file_path=file_path,
        file_name=unique_filename,
        file_type=file_type,
        description=description,
        taken_date=taken_date,
        annotations=annotations
    )

    db.add(db_medical_image)
    db.commit()
    db.refresh(db_medical_image)

    return db_medical_image

@router.get("/", response_model=List[MedicalImageResponse])
def get_medical_images(
    patient_id: Optional[str] = None,
    image_type: Optional[str] = None,
    body_part: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    query = db.query(MedicalImage)

    if patient_id:
        query = query.filter(MedicalImage.patient_id == patient_id)

    if image_type:
        query = query.filter(MedicalImage.image_type == image_type)

    if body_part:
        query = query.filter(MedicalImage.body_part == body_part)

    return query.offset(skip).limit(limit).all()

@router.get("/{image_id}", response_model=MedicalImageResponse)
def get_medical_image(
    image_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_image = db.query(MedicalImage).filter(MedicalImage.id == image_id).first()
    if db_image is None:
        raise HTTPException(status_code=404, detail="Medical image not found")
    return db_image

@router.put("/{image_id}", response_model=MedicalImageResponse)
def update_medical_image(
    image_id: str,
    image: MedicalImageUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_image = db.query(MedicalImage).filter(MedicalImage.id == image_id).first()
    if db_image is None:
        raise HTTPException(status_code=404, detail="Medical image not found")

    update_data = image.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_image, key, value)

    db_image.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_image)
    return db_image

@router.delete("/{image_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_medical_image(
    image_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_image = db.query(MedicalImage).filter(MedicalImage.id == image_id).first()
    if db_image is None:
        raise HTTPException(status_code=404, detail="Medical image not found")

    # Delete the file from storage
    try:
        if os.path.exists(db_image.file_path):
            os.remove(db_image.file_path)
    except Exception as e:
        print(f"Error deleting file: {e}")

    # Delete the database entry
    db.delete(db_image)
    db.commit()

    return None

@router.get("/file/{image_id}")
async def get_medical_image_file(
    image_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Serve the actual image file for viewing"""
    db_image = db.query(MedicalImage).filter(MedicalImage.id == image_id).first()
    if db_image is None:
        raise HTTPException(status_code=404, detail="Medical image not found")

    file_path = db_image.file_path
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Image file not found on disk")

    # Determine the media type based on file extension
    file_extension = os.path.splitext(file_path)[1].lower()
    media_type_map = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.bmp': 'image/bmp',
        '.tiff': 'image/tiff',
        '.tif': 'image/tiff',
        '.dcm': 'application/dicom',
        '.dicom': 'application/dicom',
        '.pdf': 'application/pdf'
    }

    media_type = media_type_map.get(file_extension, 'application/octet-stream')

    return FileResponse(
        path=file_path,
        media_type=media_type,
        filename=db_image.file_name
    )
