import React from 'react';
import {
  Grid,
  Box,
  useTheme,
  useMediaQuery,
  Container,
} from '@mui/material';

interface ResponsiveGridProps {
  children: React.ReactNode;
  spacing?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  container?: boolean;
  item?: boolean;
  xs?: boolean | number;
  sm?: boolean | number;
  md?: boolean | number;
  lg?: boolean | number;
  xl?: boolean | number;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  disableGutters?: boolean;
  sx?: any;
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  spacing = { xs: 2, sm: 3, md: 4 },
  container = false,
  item = false,
  xs,
  sm,
  md,
  lg,
  xl,
  maxWidth = 'xl',
  disableGutters = false,
  sx = {},
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // Auto-responsive grid sizing if not specified
  const autoXs = xs !== undefined ? xs : 12;
  const autoSm = sm !== undefined ? sm : (container ? false : 6);
  const autoMd = md !== undefined ? md : (container ? false : 4);
  const autoLg = lg !== undefined ? lg : (container ? false : 3);
  const autoXl = xl !== undefined ? xl : (container ? false : 3);

  const responsiveSpacing = typeof spacing === 'number' 
    ? spacing 
    : {
        xs: spacing.xs || 2,
        sm: spacing.sm || 3,
        md: spacing.md || 4,
        lg: spacing.lg || 4,
        xl: spacing.xl || 4,
      };

  if (container && maxWidth) {
    return (
      <Container 
        maxWidth={maxWidth} 
        disableGutters={disableGutters}
        sx={{ 
          px: { xs: 2, sm: 3, md: 4 },
          ...sx 
        }}
      >
        <Grid 
          container 
          spacing={responsiveSpacing}
          sx={{
            width: '100%',
            margin: 0,
          }}
        >
          {children}
        </Grid>
      </Container>
    );
  }

  return (
    <Grid
      container={container}
      item={item}
      spacing={container ? responsiveSpacing : undefined}
      xs={item ? autoXs : undefined}
      sm={item ? autoSm : undefined}
      md={item ? autoMd : undefined}
      lg={item ? autoLg : undefined}
      xl={item ? autoXl : undefined}
      sx={{
        width: container ? '100%' : undefined,
        margin: container ? 0 : undefined,
        ...sx,
      }}
    >
      {children}
    </Grid>
  );
};

// Responsive Grid Item Component
interface ResponsiveGridItemProps {
  children: React.ReactNode;
  xs?: boolean | number;
  sm?: boolean | number;
  md?: boolean | number;
  lg?: boolean | number;
  xl?: number;
  sx?: any;
  autoSize?: boolean;
}

export const ResponsiveGridItem: React.FC<ResponsiveGridItemProps> = ({
  children,
  xs = 12,
  sm = 6,
  md = 4,
  lg = 3,
  xl = 3,
  sx = {},
  autoSize = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // Auto-sizing based on content and screen size
  const responsiveXs = autoSize ? 12 : xs;
  const responsiveSm = autoSize ? (isMobile ? 12 : 6) : sm;
  const responsiveMd = autoSize ? (isTablet ? 6 : 4) : md;
  const responsiveLg = autoSize ? 3 : lg;
  const responsiveXl = autoSize ? 3 : xl;

  return (
    <Grid
      item
      xs={responsiveXs}
      sm={responsiveSm}
      md={responsiveMd}
      lg={responsiveLg}
      xl={responsiveXl}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        ...sx,
      }}
    >
      {children}
    </Grid>
  );
};

// Responsive Masonry Grid for dynamic content
interface ResponsiveMasonryProps {
  children: React.ReactNode;
  columns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  spacing?: number;
  sx?: any;
}

export const ResponsiveMasonry: React.FC<ResponsiveMasonryProps> = ({
  children,
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 4 },
  spacing = 2,
  sx = {},
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

  const getColumnCount = () => {
    if (isMobile) return columns.xs || 1;
    if (isTablet) return columns.sm || 2;
    if (isDesktop) return columns.lg || 4;
    return columns.md || 3;
  };

  const columnCount = getColumnCount();

  return (
    <Box
      sx={{
        columnCount,
        columnGap: theme.spacing(spacing),
        '& > *': {
          breakInside: 'avoid',
          marginBottom: theme.spacing(spacing),
          display: 'block',
          width: '100%',
        },
        ...sx,
      }}
    >
      {children}
    </Box>
  );
};

export default ResponsiveGrid;
