import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Tab,
  Tabs,
  TextField,
  Typography,
  CircularProgress,
  Alert,
  Stack,
  useTheme,
  alpha,
  Fade,
  Slide,
  Zoom,
  Avatar,
  Badge,
  Tooltip,
  CardHeader,
  CardActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Collapse,
  Skeleton,
  Fab,
  Chip,
  InputAdornment,
} from '@mui/material';

// Define the SelectChangeEvent type
type SelectChangeEvent = {
  target: {
    value: string;
  };
};
import { appointmentsAPI, patientsAPI } from '../services/api';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Event as EventIcon,
  CalendarMonth as CalendarMonthIcon,
  ViewList as ViewListIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  AccessTime as AccessTimeIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Schedule as ScheduleIcon,
  Today as TodayIcon,
  DateRange as DateRangeIcon,
  ViewModule as ViewModuleIcon,
  Notifications as NotificationsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import UniversalFilter, { type FilterField } from '../components/common/UniversalFilter';
import { useUniversalFilter, filterFunctions, sortFunctions } from '../hooks/useUniversalFilter';
import { format, isToday, isThisWeek, isThisMonth, parseISO } from 'date-fns';

// Mock data for demonstration
interface Appointment {
  id: string;
  patientId: string;
  patientName: string;
  title: string;
  description?: string;
  appointmentDate: string;
  durationMinutes: number;
  status: string;
  notes?: string;
}

const mockAppointments: Appointment[] = [
  {
    id: '1',
    patientId: '1',
    patientName: 'John Smith',
    title: 'Follow-up Consultation',
    description: 'Post-operative follow-up for knee replacement',
    appointmentDate: '2023-05-25T09:00:00',
    durationMinutes: 30,
    status: 'Scheduled',
    notes: 'Patient reports improved mobility',
  },
  {
    id: '2',
    patientId: '2',
    patientName: 'Emily Johnson',
    title: 'Initial Consultation',
    description: 'Evaluation of shoulder pain',
    appointmentDate: '2023-05-25T10:30:00',
    durationMinutes: 45,
    status: 'Scheduled',
    notes: 'New patient referral from Dr. Wilson',
  },
  {
    id: '3',
    patientId: '3',
    patientName: 'Michael Brown',
    title: 'Post-Surgery Check',
    description: 'Six-month follow-up after hip replacement',
    appointmentDate: '2023-05-26T13:15:00',
    durationMinutes: 30,
    status: 'Scheduled',
    notes: '',
  },
  {
    id: '4',
    patientId: '4',
    patientName: 'Sarah Davis',
    title: 'MRI Review',
    description: 'Review MRI results for ACL tear',
    appointmentDate: '2023-05-26T15:45:00',
    durationMinutes: 30,
    status: 'Scheduled',
    notes: 'Bring previous imaging if available',
  },
  {
    id: '5',
    patientId: '5',
    patientName: 'Robert Wilson',
    title: 'Physical Therapy Evaluation',
    description: 'Initial PT assessment for rotator cuff injury',
    appointmentDate: '2023-05-27T11:00:00',
    durationMinutes: 60,
    status: 'Scheduled',
    notes: 'Wear comfortable clothing',
  },
];

// Mock patient data for the dropdown
interface Patient {
  id: string;
  name: string;
}

const mockPatients: Patient[] = [
  { id: '1', name: 'John Smith' },
  { id: '2', name: 'Emily Johnson' },
  { id: '3', name: 'Michael Brown' },
  { id: '4', name: 'Sarah Davis' },
  { id: '5', name: 'Robert Wilson' },
];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`appointment-tabpanel-${index}`}
      aria-labelledby={`appointment-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const Appointments = () => {
  const theme = useTheme();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [tabValue, setTabValue] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'calendar'
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hoveredAppointment, setHoveredAppointment] = useState<string | null>(null);

  // Universal Filter Configuration
  const filterFields: FilterField[] = [
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'Scheduled', label: 'Scheduled', color: 'primary' },
        { value: 'Completed', label: 'Completed', color: 'success' },
        { value: 'Cancelled', label: 'Cancelled', color: 'error' },
        { value: 'No-Show', label: 'No-Show', color: 'warning' },
      ],
      width: 4,
    },
    {
      key: 'patient',
      label: 'Patient',
      type: 'select',
      options: patients.map(patient => ({
        value: patient.id,
        label: patient.name,
        avatar: patient.name.split(' ').map(n => n[0]).join(''),
      })),
      width: 4,
    },
    {
      key: 'dateRange',
      label: 'Date Range',
      type: 'dateRange',
      width: 4,
    },
  ];

  // Get base appointments filtered by tab
  const getBaseAppointments = () => {
    let result = appointments;

    if (tabValue === 0) {
      // Today
      result = result.filter((appointment) =>
        isToday(parseISO(appointment.appointmentDate))
      );
    } else if (tabValue === 1) {
      // This Week
      result = result.filter((appointment) =>
        isThisWeek(parseISO(appointment.appointmentDate))
      );
    } else if (tabValue === 2) {
      // This Month
      result = result.filter((appointment) =>
        isThisMonth(parseISO(appointment.appointmentDate))
      );
    }

    return result;
  };

  const {
    filteredData: filteredAppointments,
    searchValue,
    setSearchValue,
    filterValues,
    setFilterValue,
    sortValue,
    setSortValue,
    totalCount,
    filteredCount,
    resetAll,
  } = useUniversalFilter({
    data: getBaseAppointments(),
    searchFields: ['title', 'patientName', 'description', 'notes'],
    filterConfigs: [
      {
        key: 'status',
        defaultValue: '',
        filterFunction: (appointment, value) => filterFunctions.exact(appointment, value, 'status'),
      },
      {
        key: 'patient',
        defaultValue: '',
        filterFunction: (appointment, value) => filterFunctions.exact(appointment, value, 'patientId'),
      },
    ],
    defaultSort: 'date_asc',
    sortConfigs: {
      'date_asc': { key: 'appointmentDate', direction: 'asc', sortFunction: sortFunctions.date },
      'date_desc': { key: 'appointmentDate', direction: 'desc', sortFunction: sortFunctions.date },
      'patient_asc': { key: 'patientName', direction: 'asc', sortFunction: sortFunctions.string },
      'patient_desc': { key: 'patientName', direction: 'desc', sortFunction: sortFunctions.string },
      'status_asc': { key: 'status', direction: 'asc', sortFunction: sortFunctions.string },
    },
  });

  const sortOptions = [
    { value: 'date_asc', label: 'Date (Earliest First)' },
    { value: 'date_desc', label: 'Date (Latest First)' },
    { value: 'patient_asc', label: 'Patient (A-Z)' },
    { value: 'patient_desc', label: 'Patient (Z-A)' },
    { value: 'status_asc', label: 'Status (A-Z)' },
  ];
  const [formData, setFormData] = useState({
    patientId: '',
    title: '',
    description: '',
    appointmentDate: new Date(),
    durationMinutes: 30,
    status: 'Scheduled',
    notes: ''
  });

  // Fetch appointments and patients from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch appointments and patients in parallel
        const [appointmentsData, patientsData] = await Promise.all([
          appointmentsAPI.getAppointments(),
          patientsAPI.getPatients()
        ]);

        // Create patient map for quick lookup
        const patientMap = new Map(patientsData.map((p: any) => [p.id, `${p.first_name} ${p.last_name}`]));

        // Format appointments with patient names
        const formattedAppointments = appointmentsData.map((apt: any) => ({
          id: apt.id,
          patientId: apt.patient_id,
          patientName: patientMap.get(apt.patient_id) || 'Unknown Patient',
          title: apt.title || 'Appointment',
          description: apt.description || '',
          appointmentDate: apt.appointment_date,
          durationMinutes: apt.duration_minutes || 30,
          status: apt.status || 'Scheduled',
          notes: apt.notes || ''
        }));

        setAppointments(formattedAppointments);
        setPatients(patientsData.map((p: any) => ({
          id: p.id,
          name: `${p.first_name} ${p.last_name}`
        })));

      } catch (err) {
        console.error('Error fetching appointments data:', err);
        setError('Failed to load appointments. Please try again later.');
        // Fall back to mock data
        setAppointments(mockAppointments);
        setPatients(mockPatients);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleViewModeChange = (mode: string) => {
    setViewMode(mode);
  };

  const handleAddAppointment = () => {
    setSelectedAppointment(null);
    setFormData({
      patientId: '',
      title: '',
      description: '',
      appointmentDate: new Date(),
      durationMinutes: 30,
      status: 'Scheduled',
      notes: ''
    });
    setOpenDialog(true);
  };

  const handleEditAppointment = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setFormData({
      patientId: appointment.patientId,
      title: appointment.title,
      description: appointment.description || '',
      appointmentDate: new Date(appointment.appointmentDate),
      durationMinutes: appointment.durationMinutes,
      status: appointment.status,
      notes: appointment.notes || ''
    });
    setOpenDialog(true);
  };

  const handleDeleteAppointment = async (id: string) => {
    try {
      await appointmentsAPI.deleteAppointment(id);
      // Remove from local state
      setAppointments(appointments.filter((appointment) => appointment.id !== id));
    } catch (error) {
      console.error('Error deleting appointment:', error);
      setError('Failed to delete appointment. Please try again.');
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedAppointment(null);
    // Reset form data
    setFormData({
      patientId: '',
      title: '',
      description: '',
      appointmentDate: new Date(),
      durationMinutes: 30,
      status: 'Scheduled',
      notes: ''
    });
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveAppointment = async () => {
    // Clear previous errors
    setError('');

    // Validate required fields
    if (!formData.patientId || !formData.title || !formData.appointmentDate) {
      setError('Please fill in all required fields');
      return;
    }

    // Validate appointment date
    if (formData.appointmentDate < new Date()) {
      setError('Appointment date cannot be in the past');
      return;
    }

    // Validate duration
    if (formData.durationMinutes <= 0) {
      setError('Duration must be greater than 0 minutes');
      return;
    }

    try {
      const appointmentData = {
        patient_id: formData.patientId,
        title: formData.title.trim(),
        description: formData.description?.trim() || '',
        appointment_date: formData.appointmentDate.toISOString(),
        duration_minutes: formData.durationMinutes,
        status: formData.status,
        notes: formData.notes?.trim() || ''
      };

      if (selectedAppointment) {
        // Update existing appointment
        const updatedAppointment = await appointmentsAPI.updateAppointment(selectedAppointment.id, appointmentData);
        setAppointments(appointments.map(apt =>
          apt.id === selectedAppointment.id
            ? {
                id: updatedAppointment.id,
                patientId: updatedAppointment.patient_id,
                patientName: patients.find(p => p.id === updatedAppointment.patient_id)?.name || 'Unknown Patient',
                title: updatedAppointment.title,
                description: updatedAppointment.description,
                appointmentDate: updatedAppointment.appointment_date,
                durationMinutes: updatedAppointment.duration_minutes,
                status: updatedAppointment.status,
                notes: updatedAppointment.notes
              }
            : apt
        ));
      } else {
        // Create new appointment
        const newAppointment = await appointmentsAPI.createAppointment(appointmentData);
        const formattedAppointment = {
          id: newAppointment.id,
          patientId: newAppointment.patient_id,
          patientName: patients.find(p => p.id === newAppointment.patient_id)?.name || 'Unknown Patient',
          title: newAppointment.title,
          description: newAppointment.description,
          appointmentDate: newAppointment.appointment_date,
          durationMinutes: newAppointment.duration_minutes,
          status: newAppointment.status,
          notes: newAppointment.notes
        };
        setAppointments([...appointments, formattedAppointment]);
      }

      handleCloseDialog();
    } catch (error: any) {
      console.error('Error saving appointment:', error);

      // Handle specific error messages from the backend
      if (error.response?.data?.detail) {
        setError(error.response.data.detail);
      } else if (error.response?.status === 400) {
        setError('Invalid appointment data. Please check your inputs.');
      } else if (error.response?.status === 404) {
        setError('Patient not found. Please select a valid patient.');
      } else if (error.response?.status === 500) {
        setError('Server error. Please try again later.');
      } else {
        setError('Failed to save appointment. Please try again.');
      }
    }
  };

  // Get unique statuses for filter
  const statuses = Array.from(new Set(appointments.map((appointment) => appointment.status)));

  const renderAppointments = () => {
    if (filteredAppointments.length === 0) {
      return (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            No appointments found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {tabValue === 0 && "No appointments scheduled for today"}
            {tabValue === 1 && "No appointments scheduled for this week"}
            {tabValue === 2 && "No appointments scheduled for this month"}
            {tabValue === 3 && "No appointments found"}
          </Typography>
        </Box>
      );
    }

    return (
      <Box sx={{ p: 2 }}>
        {filteredAppointments.map((appointment) => (
          <Card key={appointment.id} sx={{ mb: 2 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <Typography variant="h6">{appointment.title}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {appointment.patientName}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={2}>
                  <Typography variant="body2">
                    {format(parseISO(appointment.appointmentDate), 'MMM dd, yyyy')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {format(parseISO(appointment.appointmentDate), 'h:mm a')}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={2}>
                  <Typography variant="body2">
                    {appointment.durationMinutes} minutes
                  </Typography>
                </Grid>
                <Grid item xs={12} md={2}>
                  <Typography
                    variant="body2"
                    sx={{
                      color:
                        appointment.status === 'Completed'
                          ? 'success.main'
                          : appointment.status === 'Cancelled'
                          ? 'error.main'
                          : appointment.status === 'No-Show'
                          ? 'warning.main'
                          : 'primary.main',
                    }}
                  >
                    {appointment.status}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <IconButton
                      size="small"
                      onClick={() => handleEditAppointment(appointment)}
                      sx={{ mr: 1 }}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteAppointment(appointment.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </Grid>
              </Grid>
              {appointment.description && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {appointment.description}
                </Typography>
              )}
              {appointment.notes && (
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Notes: {appointment.notes}
                </Typography>
              )}
            </CardContent>
          </Card>
        ))}
      </Box>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Enhanced Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems={{ xs: 'flex-start', sm: 'center' }} spacing={2}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'secondary.main' }}>
              Appointment Scheduler
            </Typography>
            <Typography variant="body1" sx={{ color: 'text.secondary', mb: 2 }}>
              Manage patient appointments, schedule consultations, and track visit history
            </Typography>
            <Stack direction="row" spacing={2} alignItems="center">
              <Chip
                icon={<EventIcon />}
                label={`${appointments.length} Total Appointments`}
                color="secondary"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<TodayIcon />}
                label={`${appointments.filter(apt => isToday(parseISO(apt.appointmentDate))).length} Today`}
                color="primary"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<ScheduleIcon />}
                label={`${appointments.filter(apt => apt.status === 'Scheduled').length} Scheduled`}
                color="success"
                variant="outlined"
                size="small"
              />
            </Stack>
          </Box>
          <Stack direction="row" spacing={1}>
            <Tooltip title="Refresh Data">
              <IconButton
                onClick={() => window.location.reload()}
                sx={{
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                  '&:hover': {
                    bgcolor: 'action.hover',
                  }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Notifications">
              <IconButton
                sx={{
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                  '&:hover': {
                    bgcolor: 'action.hover',
                  }
                }}
              >
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddAppointment}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1.5,
                boxShadow: theme.shadows[3],
                '&:hover': {
                  boxShadow: theme.shadows[6],
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              Schedule Appointment
            </Button>
          </Stack>
        </Stack>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Universal Filter Component */}
      <UniversalFilter
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        searchPlaceholder="Search appointments by patient name, title, or notes..."
        filters={filterFields}
        filterValues={filterValues}
        onFilterChange={setFilterValue}
        sortOptions={sortOptions}
        sortValue={sortValue}
        onSortChange={setSortValue}
        totalCount={totalCount}
        filteredCount={filteredCount}
        title="Appointment Search & Filter"
        collapsible={true}
        defaultExpanded={false}
        showResultCount={true}
        onClearAll={resetAll}
        customActions={
          <Tooltip title={viewMode === 'list' ? 'Switch to Calendar' : 'Switch to List'}>
            <IconButton
              onClick={() => setViewMode(viewMode === 'list' ? 'calendar' : 'list')}
              sx={{
                bgcolor: 'action.hover',
                '&:hover': {
                  bgcolor: 'action.selected',
                }
              }}
            >
              {viewMode === 'list' ? <CalendarMonthIcon /> : <ViewListIcon />}
            </IconButton>
          </Tooltip>
        }
      />

      {/* Enhanced Tabs Section */}
      <Paper
        elevation={0}
        sx={{
          borderRadius: 3,
          border: '1px solid',
          borderColor: 'divider',
          overflow: 'hidden'
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="appointment date filter tabs"
          variant="fullWidth"
          sx={{
            bgcolor: 'grey.50',
            '& .MuiTab-root': {
              fontWeight: 600,
              textTransform: 'none',
              fontSize: '0.9rem',
              '&.Mui-selected': {
                bgcolor: 'background.paper',
                color: 'primary.main',
              }
            },
            '& .MuiTabs-indicator': {
              height: 3,
              borderRadius: '3px 3px 0 0',
            }
          }}
        >
          <Tab
            icon={<TodayIcon />}
            label={`Today (${appointments.filter(apt => isToday(parseISO(apt.appointmentDate))).length})`}
            iconPosition="start"
          />
          <Tab
            icon={<DateRangeIcon />}
            label={`This Week (${appointments.filter(apt => isThisWeek(parseISO(apt.appointmentDate))).length})`}
            iconPosition="start"
          />
          <Tab
            icon={<CalendarMonthIcon />}
            label={`This Month (${appointments.filter(apt => isThisMonth(parseISO(apt.appointmentDate))).length})`}
            iconPosition="start"
          />
          <Tab
            icon={<EventIcon />}
            label={`All (${appointments.length})`}
            iconPosition="start"
          />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {renderAppointments()}
        </TabPanel>
        <TabPanel value={tabValue} index={1}>
          {renderAppointments()}
        </TabPanel>
        <TabPanel value={tabValue} index={2}>
          {renderAppointments()}
        </TabPanel>
        <TabPanel value={tabValue} index={3}>
          {renderAppointments()}
        </TabPanel>
      </Paper>

      {/* Appointment Form Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedAppointment ? 'Edit Appointment' : 'Schedule New Appointment'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Patient</InputLabel>
                <Select
                  label="Patient"
                  value={formData.patientId}
                  onChange={(e) => handleFormChange('patientId', e.target.value)}
                >
                  {patients.map((patient) => (
                    <MenuItem key={patient.id} value={patient.id}>
                      {patient.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Title"
                value={formData.title}
                onChange={(e) => handleFormChange('title', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateTimePicker
                  label="Appointment Date & Time"
                  value={formData.appointmentDate}
                  onChange={(date) => handleFormChange('appointmentDate', date || new Date())}
                  sx={{ width: '100%' }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Duration (minutes)"
                type="number"
                value={formData.durationMinutes}
                onChange={(e) => handleFormChange('durationMinutes', parseInt(e.target.value) || 30)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  label="Status"
                  value={formData.status}
                  onChange={(e) => handleFormChange('status', e.target.value)}
                >
                  <MenuItem value="Scheduled">Scheduled</MenuItem>
                  <MenuItem value="Completed">Completed</MenuItem>
                  <MenuItem value="Cancelled">Cancelled</MenuItem>
                  <MenuItem value="No-Show">No-Show</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={2}
                value={formData.description}
                onChange={(e) => handleFormChange('description', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => handleFormChange('notes', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleSaveAppointment}
            disabled={!formData.patientId || !formData.title}
          >
            {selectedAppointment ? 'Update' : 'Schedule'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Appointments;
