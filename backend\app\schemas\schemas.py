from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    email: EmailStr
    full_name: str

class UserCreate(UserBase):
    password: str

class UserResponse(UserBase):
    id: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Patient schemas
class PatientBase(BaseModel):
    first_name: str
    last_name: str
    date_of_birth: datetime
    gender: str
    contact_number: str
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    emergency_contact: Optional[str] = None
    medical_history: Optional[str] = None
    allergies: Optional[str] = None

class PatientCreate(PatientBase):
    pass

class PatientUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    date_of_birth: Optional[datetime] = None
    gender: Optional[str] = None
    contact_number: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    emergency_contact: Optional[str] = None
    medical_history: Optional[str] = None
    allergies: Optional[str] = None

class PatientResponse(PatientBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Medical Image schemas
class MedicalImageBase(BaseModel):
    patient_id: str
    image_type: str
    body_part: str
    description: Optional[str] = None
    taken_date: datetime
    annotations: Optional[str] = None

class MedicalImageCreate(MedicalImageBase):
    pass

class MedicalImageUpdate(BaseModel):
    image_type: Optional[str] = None
    body_part: Optional[str] = None
    description: Optional[str] = None
    taken_date: Optional[datetime] = None
    annotations: Optional[str] = None

class MedicalImageResponse(MedicalImageBase):
    id: str
    file_path: str
    file_name: str
    file_type: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Appointment schemas
class AppointmentBase(BaseModel):
    patient_id: str
    title: str
    description: Optional[str] = None
    appointment_date: datetime
    duration_minutes: int
    status: str
    notes: Optional[str] = None

class AppointmentCreate(AppointmentBase):
    pass

class AppointmentUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    appointment_date: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class AppointmentResponse(AppointmentBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Rehabilitation Plan schemas
class RehabilitationPlanBase(BaseModel):
    patient_id: str
    title: str
    description: Optional[str] = None
    start_date: datetime
    end_date: Optional[datetime] = None
    status: str

class RehabilitationPlanCreate(RehabilitationPlanBase):
    pass

class RehabilitationPlanUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    status: Optional[str] = None

class RehabilitationPlanResponse(RehabilitationPlanBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Rehabilitation Exercise schemas
class RehabilitationExerciseBase(BaseModel):
    rehabilitation_plan_id: str
    name: str
    description: Optional[str] = None
    frequency: str
    duration: str
    instructions: Optional[str] = None

class RehabilitationExerciseCreate(RehabilitationExerciseBase):
    pass

class RehabilitationExerciseUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    frequency: Optional[str] = None
    duration: Optional[str] = None
    instructions: Optional[str] = None

class RehabilitationExerciseResponse(RehabilitationExerciseBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Rehabilitation Progress schemas
class RehabilitationProgressBase(BaseModel):
    rehabilitation_plan_id: str
    report_date: datetime
    pain_level: int = Field(ge=1, le=10)
    notes: Optional[str] = None
    progress_percentage: float = Field(ge=0, le=100)
    submitted_by_patient: bool = False
    media_files: Optional[str] = None

class RehabilitationProgressCreate(RehabilitationProgressBase):
    pass

class RehabilitationProgressUpdate(BaseModel):
    report_date: Optional[datetime] = None
    pain_level: Optional[int] = Field(None, ge=1, le=10)
    notes: Optional[str] = None
    progress_percentage: Optional[float] = Field(None, ge=0, le=100)
    submitted_by_patient: Optional[bool] = None
    media_files: Optional[str] = None

class RehabilitationProgressResponse(RehabilitationProgressBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    email: Optional[str] = None
