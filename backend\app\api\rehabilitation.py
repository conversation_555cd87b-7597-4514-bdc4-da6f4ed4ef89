from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import os
import shutil
import uuid
import json

from app.database.database import get_db
from app.models.models import (
    RehabilitationPlan, 
    RehabilitationExercise, 
    RehabilitationProgress, 
    Patient
)
from app.schemas.schemas import (
    RehabilitationPlanCreate, 
    RehabilitationPlanUpdate, 
    RehabilitationPlanResponse,
    RehabilitationExerciseCreate,
    RehabilitationExerciseUpdate,
    RehabilitationExerciseResponse,
    RehabilitationProgressCreate,
    RehabilitationProgressUpdate,
    RehabilitationProgressResponse
)
from app.auth.auth import get_current_active_user

router = APIRouter()

# Create directory for storing rehabilitation progress media if it doesn't exist
UPLOAD_DIRECTORY = "uploads/rehabilitation"
os.makedirs(UPLOAD_DIRECTORY, exist_ok=True)

# Rehabilitation Plan endpoints
@router.post("/plans", response_model=RehabilitationPlanResponse, status_code=status.HTTP_201_CREATED)
def create_rehabilitation_plan(
    plan: RehabilitationPlanCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    # Check if patient exists
    db_patient = db.query(Patient).filter(Patient.id == plan.patient_id).first()
    if not db_patient:
        raise HTTPException(status_code=404, detail="Patient not found")
    
    db_plan = RehabilitationPlan(**plan.model_dump())
    db.add(db_plan)
    db.commit()
    db.refresh(db_plan)
    return db_plan

@router.get("/plans", response_model=List[RehabilitationPlanResponse])
def get_rehabilitation_plans(
    patient_id: Optional[str] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    query = db.query(RehabilitationPlan)
    
    if patient_id:
        query = query.filter(RehabilitationPlan.patient_id == patient_id)
    
    if status:
        query = query.filter(RehabilitationPlan.status == status)
    
    return query.offset(skip).limit(limit).all()

@router.get("/plans/{plan_id}", response_model=RehabilitationPlanResponse)
def get_rehabilitation_plan(
    plan_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_plan = db.query(RehabilitationPlan).filter(RehabilitationPlan.id == plan_id).first()
    if db_plan is None:
        raise HTTPException(status_code=404, detail="Rehabilitation plan not found")
    return db_plan

@router.put("/plans/{plan_id}", response_model=RehabilitationPlanResponse)
def update_rehabilitation_plan(
    plan_id: str,
    plan: RehabilitationPlanUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_plan = db.query(RehabilitationPlan).filter(RehabilitationPlan.id == plan_id).first()
    if db_plan is None:
        raise HTTPException(status_code=404, detail="Rehabilitation plan not found")
    
    update_data = plan.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_plan, key, value)
    
    db_plan.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_plan)
    return db_plan

@router.delete("/plans/{plan_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_rehabilitation_plan(
    plan_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_plan = db.query(RehabilitationPlan).filter(RehabilitationPlan.id == plan_id).first()
    if db_plan is None:
        raise HTTPException(status_code=404, detail="Rehabilitation plan not found")
    
    db.delete(db_plan)
    db.commit()
    return None

# Rehabilitation Exercise endpoints
@router.post("/exercises", response_model=RehabilitationExerciseResponse, status_code=status.HTTP_201_CREATED)
def create_rehabilitation_exercise(
    exercise: RehabilitationExerciseCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    # Check if rehabilitation plan exists
    db_plan = db.query(RehabilitationPlan).filter(RehabilitationPlan.id == exercise.rehabilitation_plan_id).first()
    if not db_plan:
        raise HTTPException(status_code=404, detail="Rehabilitation plan not found")
    
    db_exercise = RehabilitationExercise(**exercise.model_dump())
    db.add(db_exercise)
    db.commit()
    db.refresh(db_exercise)
    return db_exercise

@router.get("/exercises", response_model=List[RehabilitationExerciseResponse])
def get_rehabilitation_exercises(
    rehabilitation_plan_id: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    query = db.query(RehabilitationExercise)
    
    if rehabilitation_plan_id:
        query = query.filter(RehabilitationExercise.rehabilitation_plan_id == rehabilitation_plan_id)
    
    return query.offset(skip).limit(limit).all()

@router.get("/exercises/{exercise_id}", response_model=RehabilitationExerciseResponse)
def get_rehabilitation_exercise(
    exercise_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_exercise = db.query(RehabilitationExercise).filter(RehabilitationExercise.id == exercise_id).first()
    if db_exercise is None:
        raise HTTPException(status_code=404, detail="Rehabilitation exercise not found")
    return db_exercise

@router.put("/exercises/{exercise_id}", response_model=RehabilitationExerciseResponse)
def update_rehabilitation_exercise(
    exercise_id: str,
    exercise: RehabilitationExerciseUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_exercise = db.query(RehabilitationExercise).filter(RehabilitationExercise.id == exercise_id).first()
    if db_exercise is None:
        raise HTTPException(status_code=404, detail="Rehabilitation exercise not found")
    
    update_data = exercise.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_exercise, key, value)
    
    db_exercise.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_exercise)
    return db_exercise

@router.delete("/exercises/{exercise_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_rehabilitation_exercise(
    exercise_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_exercise = db.query(RehabilitationExercise).filter(RehabilitationExercise.id == exercise_id).first()
    if db_exercise is None:
        raise HTTPException(status_code=404, detail="Rehabilitation exercise not found")
    
    db.delete(db_exercise)
    db.commit()
    return None

# Rehabilitation Progress endpoints
@router.post("/progress", response_model=RehabilitationProgressResponse, status_code=status.HTTP_201_CREATED)
async def create_rehabilitation_progress(
    rehabilitation_plan_id: str = Form(...),
    report_date: datetime = Form(...),
    pain_level: int = Form(...),
    notes: Optional[str] = Form(None),
    progress_percentage: float = Form(...),
    submitted_by_patient: bool = Form(False),
    files: List[UploadFile] = File(None),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    # Check if rehabilitation plan exists
    db_plan = db.query(RehabilitationPlan).filter(RehabilitationPlan.id == rehabilitation_plan_id).first()
    if not db_plan:
        raise HTTPException(status_code=404, detail="Rehabilitation plan not found")
    
    # Handle file uploads
    media_files = []
    if files:
        for file in files:
            file_extension = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join(UPLOAD_DIRECTORY, unique_filename)
            
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            media_files.append(file_path)
    
    # Create progress report
    db_progress = RehabilitationProgress(
        rehabilitation_plan_id=rehabilitation_plan_id,
        report_date=report_date,
        pain_level=pain_level,
        notes=notes,
        progress_percentage=progress_percentage,
        submitted_by_patient=submitted_by_patient,
        media_files=json.dumps(media_files) if media_files else None
    )
    
    db.add(db_progress)
    db.commit()
    db.refresh(db_progress)
    return db_progress

@router.get("/progress", response_model=List[RehabilitationProgressResponse])
def get_rehabilitation_progress_reports(
    rehabilitation_plan_id: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    query = db.query(RehabilitationProgress)
    
    if rehabilitation_plan_id:
        query = query.filter(RehabilitationProgress.rehabilitation_plan_id == rehabilitation_plan_id)
    
    return query.order_by(RehabilitationProgress.report_date.desc()).offset(skip).limit(limit).all()

@router.get("/progress/{progress_id}", response_model=RehabilitationProgressResponse)
def get_rehabilitation_progress(
    progress_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_progress = db.query(RehabilitationProgress).filter(RehabilitationProgress.id == progress_id).first()
    if db_progress is None:
        raise HTTPException(status_code=404, detail="Rehabilitation progress report not found")
    return db_progress

@router.put("/progress/{progress_id}", response_model=RehabilitationProgressResponse)
def update_rehabilitation_progress(
    progress_id: str,
    progress: RehabilitationProgressUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_progress = db.query(RehabilitationProgress).filter(RehabilitationProgress.id == progress_id).first()
    if db_progress is None:
        raise HTTPException(status_code=404, detail="Rehabilitation progress report not found")
    
    update_data = progress.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_progress, key, value)
    
    db_progress.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_progress)
    return db_progress

@router.delete("/progress/{progress_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_rehabilitation_progress(
    progress_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_progress = db.query(RehabilitationProgress).filter(RehabilitationProgress.id == progress_id).first()
    if db_progress is None:
        raise HTTPException(status_code=404, detail="Rehabilitation progress report not found")
    
    # Delete media files if they exist
    if db_progress.media_files:
        try:
            media_files = json.loads(db_progress.media_files)
            for file_path in media_files:
                if os.path.exists(file_path):
                    os.remove(file_path)
        except Exception as e:
            print(f"Error deleting media files: {e}")
    
    db.delete(db_progress)
    db.commit()
    return None
