from fastapi import APIRouter, Depends, HTTPException, status, Response
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from app.database.database import get_db
from app.models.models import Patient, MedicalImage, Appointment, RehabilitationPlan, RehabilitationExercise
from app.auth.auth import get_current_active_user
from app.utils.pdf_generator import generate_patient_report

router = APIRouter()

@router.get("/patient/{patient_id}/pdf")
def generate_patient_pdf_report(
    patient_id: str,
    include_images: bool = True,
    include_appointments: bool = True,
    include_rehabilitation: bool = True,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    # Get patient
    patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not patient:
        raise HTTPException(status_code=404, detail="Patient not found")
    
    # Get related data
    medical_images = None
    appointments = None
    rehabilitation_plans = None
    
    if include_images:
        medical_images = db.query(MedicalImage).filter(MedicalImage.patient_id == patient_id).all()
    
    if include_appointments:
        appointments = db.query(Appointment).filter(Appointment.patient_id == patient_id).all()
    
    if include_rehabilitation:
        rehabilitation_plans = db.query(RehabilitationPlan).filter(RehabilitationPlan.patient_id == patient_id).all()
        
        # Load exercises for each plan
        for plan in rehabilitation_plans:
            plan.exercises = db.query(RehabilitationExercise).filter(
                RehabilitationExercise.rehabilitation_plan_id == plan.id
            ).all()
    
    # Generate PDF
    try:
        pdf_data = generate_patient_report(
            patient=patient,
            medical_images=medical_images,
            appointments=appointments,
            rehabilitation_plans=rehabilitation_plans
        )
        
        # Return PDF as response
        filename = f"patient_report_{patient.last_name}_{patient.first_name}_{datetime.now().strftime('%Y%m%d')}.pdf"
        return Response(
            content=pdf_data,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating PDF: {str(e)}")
