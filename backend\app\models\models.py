from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text, DateTime, Float, Table
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from app.database.database import Base

def generate_uuid():
    return str(uuid.uuid4())

class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True, default=generate_uuid)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Patient(Base):
    __tablename__ = "patients"

    id = Column(String, primary_key=True, default=generate_uuid)
    first_name = Column(String, index=True)
    last_name = Column(String, index=True)
    date_of_birth = Column(DateTime)
    gender = Column(String)
    contact_number = Column(String)
    email = Column(String)
    address = Column(String)
    emergency_contact = Column(String)
    medical_history = Column(Text)
    allergies = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    medical_images = relationship("MedicalImage", back_populates="patient")
    appointments = relationship("Appointment", back_populates="patient")
    rehabilitation_plans = relationship("RehabilitationPlan", back_populates="patient")

class MedicalImage(Base):
    __tablename__ = "medical_images"

    id = Column(String, primary_key=True, default=generate_uuid)
    patient_id = Column(String, ForeignKey("patients.id"))
    image_type = Column(String)  # X-ray, MRI, CT scan, etc.
    body_part = Column(String)
    file_path = Column(String)
    file_name = Column(String)
    file_type = Column(String)  # DICOM, JPEG, PNG, etc.
    description = Column(Text)
    taken_date = Column(DateTime)
    annotations = Column(Text)  # JSON string of annotations
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    patient = relationship("Patient", back_populates="medical_images")

class Appointment(Base):
    __tablename__ = "appointments"

    id = Column(String, primary_key=True, default=generate_uuid)
    patient_id = Column(String, ForeignKey("patients.id"))
    title = Column(String)
    description = Column(Text)
    appointment_date = Column(DateTime)
    duration_minutes = Column(Integer)
    status = Column(String)  # Scheduled, Completed, Cancelled, etc.
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    patient = relationship("Patient", back_populates="appointments")

class RehabilitationPlan(Base):
    __tablename__ = "rehabilitation_plans"

    id = Column(String, primary_key=True, default=generate_uuid)
    patient_id = Column(String, ForeignKey("patients.id"))
    title = Column(String)
    description = Column(Text)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    status = Column(String)  # Active, Completed, Cancelled, etc.
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    patient = relationship("Patient", back_populates="rehabilitation_plans")
    exercises = relationship("RehabilitationExercise", back_populates="rehabilitation_plan")
    progress_reports = relationship("RehabilitationProgress", back_populates="rehabilitation_plan")

class RehabilitationExercise(Base):
    __tablename__ = "rehabilitation_exercises"

    id = Column(String, primary_key=True, default=generate_uuid)
    rehabilitation_plan_id = Column(String, ForeignKey("rehabilitation_plans.id"))
    name = Column(String)
    description = Column(Text)
    frequency = Column(String)  # e.g., "3 times per day"
    duration = Column(String)  # e.g., "15 minutes"
    instructions = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    rehabilitation_plan = relationship("RehabilitationPlan", back_populates="exercises")

class RehabilitationProgress(Base):
    __tablename__ = "rehabilitation_progress"

    id = Column(String, primary_key=True, default=generate_uuid)
    rehabilitation_plan_id = Column(String, ForeignKey("rehabilitation_plans.id"))
    report_date = Column(DateTime)
    pain_level = Column(Integer)  # Scale 1-10
    notes = Column(Text)
    progress_percentage = Column(Float)
    submitted_by_patient = Column(Boolean, default=False)
    media_files = Column(Text)  # JSON string of file paths
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    rehabilitation_plan = relationship("RehabilitationPlan", back_populates="progress_reports")
