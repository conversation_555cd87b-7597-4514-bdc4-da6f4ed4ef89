import React from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardActions,
  Box,
  useTheme,
  useMediaQuery,
  alpha,
  Skeleton,
} from '@mui/material';

interface ResponsiveCardProps {
  children?: React.ReactNode;
  title?: React.ReactNode;
  subtitle?: React.ReactNode;
  action?: React.ReactNode;
  actions?: React.ReactNode;
  loading?: boolean;
  elevation?: number;
  variant?: 'outlined' | 'elevation';
  sx?: any;
  contentSx?: any;
  headerSx?: any;
  actionsSx?: any;
  hover?: boolean;
  clickable?: boolean;
  onClick?: () => void;
  gradient?: boolean;
  fullHeight?: boolean;
}

const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  title,
  subtitle,
  action,
  actions,
  loading = false,
  elevation = 0,
  variant = 'outlined',
  sx = {},
  contentSx = {},
  headerSx = {},
  actionsSx = {},
  hover = false,
  clickable = false,
  onClick,
  gradient = false,
  fullHeight = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const cardSx = {
    borderRadius: { xs: 2, sm: 3, md: 4 },
    border: variant === 'outlined' ? '1px solid' : 'none',
    borderColor: 'divider',
    boxShadow: variant === 'elevation' ? theme.shadows[elevation] : 'none',
    transition: 'all 0.3s ease-in-out',
    cursor: clickable ? 'pointer' : 'default',
    height: fullHeight ? '100%' : 'auto',
    display: 'flex',
    flexDirection: 'column',
    background: gradient 
      ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`
      : 'background.paper',
    '&:hover': hover ? {
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[8],
      borderColor: 'primary.main',
    } : {},
    ...sx,
  };

  const responsiveHeaderSx = {
    pb: title && subtitle ? 1 : 2,
    px: { xs: 2, sm: 3 },
    pt: { xs: 2, sm: 3 },
    ...headerSx,
  };

  const responsiveContentSx = {
    px: { xs: 2, sm: 3 },
    py: { xs: 1, sm: 2 },
    flex: fullHeight ? 1 : 'none',
    '&:last-child': {
      pb: { xs: 2, sm: 3 },
    },
    ...contentSx,
  };

  const responsiveActionsSx = {
    px: { xs: 2, sm: 3 },
    pb: { xs: 2, sm: 3 },
    pt: 0,
    ...actionsSx,
  };

  if (loading) {
    return (
      <Card sx={cardSx}>
        {(title || subtitle || action) && (
          <CardHeader
            title={<Skeleton variant="text" width="60%" height={32} />}
            subheader={subtitle && <Skeleton variant="text" width="40%" height={20} />}
            action={action && <Skeleton variant="circular" width={40} height={40} />}
            sx={responsiveHeaderSx}
          />
        )}
        <CardContent sx={responsiveContentSx}>
          <Skeleton variant="rectangular" height={isMobile ? 120 : 160} />
          <Box sx={{ mt: 2 }}>
            <Skeleton variant="text" height={20} />
            <Skeleton variant="text" height={20} width="80%" />
            <Skeleton variant="text" height={20} width="60%" />
          </Box>
        </CardContent>
        {actions && (
          <CardActions sx={responsiveActionsSx}>
            <Skeleton variant="rectangular" width={80} height={36} />
            <Skeleton variant="rectangular" width={80} height={36} />
          </CardActions>
        )}
      </Card>
    );
  }

  return (
    <Card sx={cardSx} onClick={clickable ? onClick : undefined}>
      {(title || subtitle || action) && (
        <CardHeader
          title={title}
          subheader={subtitle}
          action={action}
          sx={responsiveHeaderSx}
          titleTypographyProps={{
            variant: isMobile ? 'h6' : 'h5',
            fontWeight: 600,
          }}
          subheaderTypographyProps={{
            variant: isMobile ? 'body2' : 'body1',
            color: 'text.secondary',
          }}
        />
      )}
      
      <CardContent sx={responsiveContentSx}>
        {children}
      </CardContent>
      
      {actions && (
        <CardActions sx={responsiveActionsSx}>
          {actions}
        </CardActions>
      )}
    </Card>
  );
};

export default ResponsiveCard;
