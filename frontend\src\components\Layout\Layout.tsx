import { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Avatar,
  Box,
  CssBaseline,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Menu,
  MenuItem,
  Tooltip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Image as ImageIcon,
  Event as EventIcon,
  FitnessCenter as FitnessCenterIcon,
  Person as PersonIcon,
  AccountCircle as AccountCircleIcon,
  Logout as LogoutIcon,
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

const drawerWidth = 280;
const mobileDrawerWidth = 320;
const appBarHeight = 72;

interface MenuItem {
  text: string;
  icon: React.ReactNode;
  path: string;
}

const menuItems: MenuItem[] = [
  { text: 'Dashboard', icon: <DashboardIcon />, path: '/' },
  { text: 'Patients', icon: <PeopleIcon />, path: '/patients' },
  { text: 'Medical Images', icon: <ImageIcon />, path: '/medical-images' },
  { text: 'Appointments', icon: <EventIcon />, path: '/appointments' },
  { text: 'Rehabilitation', icon: <FitnessCenterIcon />, path: '/rehabilitation' },
  { text: 'Profile', icon: <PersonIcon />, path: '/profile' },
];

const Layout = () => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    // Use the logout function from auth context
    logout();
    handleMenuClose();
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    setMobileOpen(false);
  };

  const drawer = (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      bgcolor: 'background.paper',
      borderRight: '1px solid',
      borderColor: 'divider'
    }}>
      {/* Header Section */}
      <Box sx={{
        p: 3,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        borderBottom: '1px solid',
        borderColor: 'divider',
        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
        color: 'white'
      }}>
        <Avatar
          src="/dr-sharma-profile.svg"
          alt="Dr. Siddhartha Sharma"
          sx={{
            width: 72,
            height: 72,
            mb: 2,
            border: '3px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            bgcolor: 'primary.main'
          }}
        >
          DS
        </Avatar>
        <Typography variant="h6" sx={{
          fontWeight: 700,
          textAlign: 'center',
          mb: 0.5
        }}>
          Ortho EHR
        </Typography>
        <Typography variant="caption" sx={{
          color: 'rgba(255, 255, 255, 0.9)',
          textAlign: 'center',
          fontSize: '0.75rem',
          fontWeight: 500
        }}>
          Dr. Siddhartha Sharma
        </Typography>
        <Typography variant="caption" sx={{
          color: 'rgba(255, 255, 255, 0.7)',
          textAlign: 'center',
          fontSize: '0.7rem',
          lineHeight: 1.2
        }}>
          PGIMER Chandigarh
        </Typography>
      </Box>

      {/* Navigation Menu */}
      <Box sx={{ flex: 1, px: 2, py: 3 }}>
        <Typography variant="overline" sx={{
          px: 2,
          color: 'text.secondary',
          fontWeight: 600,
          fontSize: '0.7rem',
          letterSpacing: '0.1em'
        }}>
          Navigation
        </Typography>
        <List sx={{ mt: 1 }}>
          {menuItems.map((item) => (
            <ListItem key={item.text} disablePadding sx={{ mb: 1 }}>
              <ListItemButton
                selected={location.pathname === item.path}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  borderRadius: 2,
                  py: 1.5,
                  px: 2,
                  transition: 'all 0.2s ease-in-out',
                  '&.Mui-selected': {
                    bgcolor: 'primary.main',
                    color: 'primary.contrastText',
                    boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
                    '& .MuiListItemIcon-root': {
                      color: 'primary.contrastText',
                    },
                    '&:hover': {
                      bgcolor: 'primary.dark',
                    },
                  },
                  '&:hover': {
                    bgcolor: 'action.hover',
                    transform: 'translateX(4px)',
                  },
                }}
              >
                <ListItemIcon sx={{
                  minWidth: 44,
                  color: location.pathname === item.path ? 'inherit' : 'text.secondary'
                }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontWeight: location.pathname === item.path ? 600 : 500,
                    fontSize: '0.9rem'
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Footer */}
      <Box sx={{
        p: 3,
        borderTop: '1px solid',
        borderColor: 'divider',
        bgcolor: 'grey.50'
      }}>
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Typography variant="caption" sx={{
            color: 'text.secondary',
            fontSize: '0.7rem',
            fontWeight: 500,
            display: 'block'
          }}>
            Additional Professor
          </Typography>
          <Typography variant="caption" sx={{
            color: 'text.secondary',
            fontSize: '0.65rem',
            display: 'block'
          }}>
            Department of Orthopaedics
          </Typography>
        </Box>
        <Typography variant="caption" sx={{
          color: 'text.secondary',
          fontSize: '0.6rem',
          display: 'block',
          textAlign: 'center',
          opacity: 0.7
        }}>
          © {new Date().getFullYear()} PGIMER Chandigarh
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          height: appBarHeight,
          bgcolor: 'background.paper',
          color: 'text.primary',
          borderBottom: '1px solid',
          borderColor: 'divider',
          backdropFilter: 'blur(8px)',
          background: 'rgba(255, 255, 255, 0.95)',
        }}
      >
        <Toolbar sx={{
          justifyContent: 'space-between',
          minHeight: appBarHeight,
          px: { xs: 2, sm: 3 }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{
                mr: 2,
                display: { sm: 'none' },
                bgcolor: 'action.hover',
                '&:hover': {
                  bgcolor: 'action.selected',
                }
              }}
            >
              <MenuIcon />
            </IconButton>

            {/* Page Title Section */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                <Typography
                  variant="h5"
                  component="div"
                  sx={{
                    fontWeight: 700,
                    color: 'text.primary',
                    mb: 0.5
                  }}
                >
                  {menuItems.find(item => item.path === location.pathname)?.text || 'Dashboard'}
                </Typography>
                <Typography
                  variant="body2"
                  component="div"
                  sx={{
                    color: 'text.secondary',
                    fontSize: '0.875rem',
                    fontWeight: 500
                  }}
                >
                  Dr. Siddhartha Sharma • PGIMER Chandigarh
                </Typography>
              </Box>

              {/* Mobile Title */}
              <Box sx={{ display: { xs: 'block', sm: 'none' } }}>
                <Typography
                  variant="h6"
                  component="div"
                  sx={{
                    fontWeight: 600,
                    color: 'text.primary'
                  }}
                >
                  {menuItems.find(item => item.path === location.pathname)?.text || 'Dashboard'}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* User Profile Section */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* User Info - Desktop */}
            {user && (
              <Box sx={{
                display: { xs: 'none', md: 'flex' },
                alignItems: 'center',
                bgcolor: 'grey.50',
                borderRadius: 2,
                px: 2,
                py: 1
              }}>
                <Box sx={{ textAlign: 'right', mr: 1.5 }}>
                  <Typography variant="body2" sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                    lineHeight: 1.2
                  }}>
                    {user.full_name}
                  </Typography>
                  <Typography variant="caption" sx={{
                    color: 'text.secondary',
                    fontSize: '0.75rem'
                  }}>
                    {user.email}
                  </Typography>
                </Box>
              </Box>
            )}

            {/* Profile Avatar */}
            <Tooltip title="Account settings">
              <IconButton
                onClick={handleMenuClick}
                size="small"
                sx={{
                  p: 0.5,
                  border: '2px solid',
                  borderColor: 'divider',
                  '&:hover': {
                    borderColor: 'primary.main',
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
                aria-controls={Boolean(anchorEl) ? 'account-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={Boolean(anchorEl) ? 'true' : undefined}
              >
                <Avatar
                  src="/dr-sharma-profile.svg"
                  alt="Dr. Siddhartha Sharma"
                  sx={{
                    width: 40,
                    height: 40,
                    bgcolor: 'primary.main',
                    fontSize: '1rem',
                    fontWeight: 'bold',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  DS
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            PaperProps={{
              elevation: 2,
              sx: {
                minWidth: 200,
                borderRadius: 1,
                mt: 1,
                overflow: 'visible',
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: 'translateY(-50%) rotate(45deg)',
                  zIndex: 0,
                },
              },
            }}
          >
            {user && (
              <Box sx={{ px: 2, py: 1.5 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>{user.full_name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {user.email}
                </Typography>
              </Box>
            )}
            <Divider />
            <MenuItem onClick={() => { handleNavigation('/profile'); handleMenuClose(); }} sx={{ py: 1.5 }}>
              <ListItemIcon>
                <PersonIcon fontSize="small" color="primary" />
              </ListItemIcon>
              <ListItemText primary="Profile" />
            </MenuItem>
            <MenuItem onClick={handleLogout} sx={{ py: 1.5 }}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" color="error" />
              </ListItemIcon>
              <ListItemText primary="Logout" primaryTypographyProps={{ color: 'error.main' }} />
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="mailbox folders"
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: mobileDrawerWidth,
              borderRadius: { xs: '0 16px 16px 0', sm: 0 },
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          bgcolor: 'background.default',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Toolbar sx={{ minHeight: appBarHeight }} />
        <Box sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3, md: 4 },
          maxWidth: '1600px',
          mx: 'auto',
          width: '100%'
        }}>
          <Outlet />
        </Box>
      </Box>
    </Box>
  );
};

export default Layout;
