import { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Avatar,
  Box,
  CssBaseline,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Menu,
  MenuItem,
  Tooltip,
  useTheme,
  useMediaQuery,
  alpha,
  Stack,
  Chip,
  Badge,
  Fade,
  Slide,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Image as ImageIcon,
  Event as EventIcon,
  FitnessCenter as FitnessCenterIcon,
  Analytics as AnalyticsIcon,
  Person as PersonIcon,
  AccountCircle as AccountCircleIcon,
  Logout as LogoutIcon,
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

const drawerWidth = 280;
const mobileDrawerWidth = 320;
const appBarHeight = 80;
const collapsedDrawerWidth = 72;

interface MenuItem {
  text: string;
  icon: React.ReactNode;
  path: string;
}

const menuItems: MenuItem[] = [
  { text: 'Dashboard', icon: <DashboardIcon />, path: '/' },
  { text: 'Patients', icon: <PeopleIcon />, path: '/patients' },
  { text: 'Medical Images', icon: <ImageIcon />, path: '/medical-images' },
  { text: 'Appointments', icon: <EventIcon />, path: '/appointments' },
  { text: 'Rehabilitation', icon: <FitnessCenterIcon />, path: '/rehabilitation' },
  { text: 'Analytics', icon: <AnalyticsIcon />, path: '/analytics' },
  { text: 'Profile', icon: <PersonIcon />, path: '/profile' },
];

const Layout = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const isTablet = useMediaQuery(theme.breakpoints.down('xl'));

  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [drawerCollapsed, setDrawerCollapsed] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();

  const currentDrawerWidth = isMobile ? mobileDrawerWidth : (drawerCollapsed ? collapsedDrawerWidth : drawerWidth);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    // Use the logout function from auth context
    logout();
    handleMenuClose();
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    setMobileOpen(false);
  };

  const drawer = (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      bgcolor: 'background.paper',
      borderRight: '1px solid',
      borderColor: 'divider',
      overflow: 'hidden'
    }}>
      {/* Header Section */}
      <Box sx={{
        p: drawerCollapsed && !isMobile ? 2 : 3,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        borderBottom: '1px solid',
        borderColor: 'divider',
        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          opacity: 0.3,
        }
      }}>
        <Avatar
          src="/dr-sharma-profile.svg"
          alt="Dr. Siddhartha Sharma"
          sx={{
            width: drawerCollapsed && !isMobile ? 48 : 72,
            height: drawerCollapsed && !isMobile ? 48 : 72,
            mb: drawerCollapsed && !isMobile ? 1 : 2,
            border: '3px solid rgba(255, 255, 255, 0.2)',
            boxShadow: theme.shadows[4],
            bgcolor: 'primary.main',
            transition: 'all 0.3s ease-in-out',
            zIndex: 1,
          }}
        >
          DS
        </Avatar>
        {(!drawerCollapsed || isMobile) && (
          <Fade in={!drawerCollapsed || isMobile}>
            <Box sx={{ textAlign: 'center', zIndex: 1 }}>
              <Typography variant="h6" sx={{
                fontWeight: 700,
                textAlign: 'center',
                mb: 0.5
              }}>
                Ortho EHR
              </Typography>
              <Typography variant="caption" sx={{
                color: 'rgba(255, 255, 255, 0.9)',
                textAlign: 'center',
                fontSize: '0.75rem',
                fontWeight: 500,
                display: 'block'
              }}>
                Dr. Siddhartha Sharma
              </Typography>
              <Typography variant="caption" sx={{
                color: 'rgba(255, 255, 255, 0.7)',
                textAlign: 'center',
                fontSize: '0.7rem',
                lineHeight: 1.2,
                display: 'block'
              }}>
                PGIMER Chandigarh
              </Typography>
            </Box>
          </Fade>
        )}
      </Box>

      {/* Navigation Menu */}
      <Box sx={{ flex: 1, px: drawerCollapsed && !isMobile ? 1 : 2, py: 3 }}>
        {(!drawerCollapsed || isMobile) && (
          <Typography variant="overline" sx={{
            px: 2,
            color: 'text.secondary',
            fontWeight: 600,
            fontSize: '0.7rem',
            letterSpacing: '0.1em'
          }}>
            Navigation
          </Typography>
        )}
        <List sx={{ mt: 1 }}>
          {menuItems.map((item, index) => (
            <ListItem key={item.text} disablePadding sx={{ mb: 1 }}>
              <Tooltip
                title={drawerCollapsed && !isMobile ? item.text : ''}
                placement="right"
                arrow
              >
                <ListItemButton
                  selected={location.pathname === item.path}
                  onClick={() => handleNavigation(item.path)}
                  sx={{
                    borderRadius: 3,
                    py: drawerCollapsed && !isMobile ? 2 : 1.5,
                    px: drawerCollapsed && !isMobile ? 1.5 : 2,
                    transition: 'all 0.3s ease-in-out',
                    justifyContent: drawerCollapsed && !isMobile ? 'center' : 'flex-start',
                    minHeight: 48,
                    '&.Mui-selected': {
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      color: 'primary.main',
                      borderLeft: '4px solid',
                      borderLeftColor: 'primary.main',
                      '& .MuiListItemIcon-root': {
                        color: 'primary.main',
                      },
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.15),
                      },
                    },
                    '&:hover': {
                      bgcolor: 'action.hover',
                      transform: drawerCollapsed && !isMobile ? 'scale(1.05)' : 'translateX(4px)',
                    },
                  }}
                >
                  <ListItemIcon sx={{
                    minWidth: drawerCollapsed && !isMobile ? 'auto' : 44,
                    color: location.pathname === item.path ? 'inherit' : 'text.secondary',
                    justifyContent: 'center'
                  }}>
                    <Badge
                      color="error"
                      variant="dot"
                      invisible={item.text !== 'Appointments'} // Example notification
                      sx={{
                        '& .MuiBadge-badge': {
                          right: -2,
                          top: 2,
                        }
                      }}
                    >
                      {item.icon}
                    </Badge>
                  </ListItemIcon>
                  {(!drawerCollapsed || isMobile) && (
                    <ListItemText
                      primary={item.text}
                      primaryTypographyProps={{
                        fontWeight: location.pathname === item.path ? 600 : 500,
                        fontSize: '0.9rem'
                      }}
                    />
                  )}
                </ListItemButton>
              </Tooltip>
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Footer */}
      <Box sx={{
        p: 3,
        borderTop: '1px solid',
        borderColor: 'divider',
        bgcolor: 'grey.50'
      }}>
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Typography variant="caption" sx={{
            color: 'text.secondary',
            fontSize: '0.7rem',
            fontWeight: 500,
            display: 'block'
          }}>
            Additional Professor
          </Typography>
          <Typography variant="caption" sx={{
            color: 'text.secondary',
            fontSize: '0.65rem',
            display: 'block'
          }}>
            Department of Orthopaedics
          </Typography>
        </Box>
        <Typography variant="caption" sx={{
          color: 'text.secondary',
          fontSize: '0.6rem',
          display: 'block',
          textAlign: 'center',
          opacity: 0.7
        }}>
          © {new Date().getFullYear()} PGIMER Chandigarh
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          width: { lg: `calc(100% - ${currentDrawerWidth}px)` },
          ml: { lg: `${currentDrawerWidth}px` },
          height: appBarHeight,
          bgcolor: alpha(theme.palette.background.paper, 0.95),
          color: 'text.primary',
          borderBottom: '1px solid',
          borderColor: 'divider',
          backdropFilter: 'blur(20px)',
          transition: 'all 0.3s ease-in-out',
          zIndex: theme.zIndex.drawer - 1,
        }}
      >
        <Toolbar sx={{
          justifyContent: 'space-between',
          minHeight: appBarHeight,
          px: { xs: 2, sm: 3, md: 4 }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{
                display: { lg: 'none' },
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: 'primary.main',
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.2),
                  transform: 'scale(1.05)',
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <MenuIcon />
            </IconButton>

            {/* Drawer Collapse Toggle for Desktop */}
            {!isMobile && (
              <IconButton
                color="inherit"
                onClick={() => setDrawerCollapsed(!drawerCollapsed)}
                sx={{
                  bgcolor: alpha(theme.palette.grey[500], 0.1),
                  color: 'text.secondary',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.grey[500], 0.2),
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                <MenuIcon />
              </IconButton>
            )}

            {/* Page Title Section */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ display: { xs: 'none', md: 'block' } }}>
                <Typography
                  variant="h5"
                  component="div"
                  sx={{
                    fontWeight: 700,
                    color: 'text.primary',
                    mb: 0.5
                  }}
                >
                  {menuItems.find(item => item.path === location.pathname)?.text || 'Dashboard'}
                </Typography>
                <Typography
                  variant="body2"
                  component="div"
                  sx={{
                    color: 'text.secondary',
                    fontSize: '0.875rem',
                    fontWeight: 500
                  }}
                >
                  Dr. Siddhartha Sharma • PGIMER Chandigarh
                </Typography>
              </Box>

              {/* Mobile/Tablet Title */}
              <Box sx={{ display: { xs: 'block', md: 'none' } }}>
                <Typography
                  variant="h6"
                  component="div"
                  sx={{
                    fontWeight: 600,
                    color: 'text.primary'
                  }}
                >
                  {menuItems.find(item => item.path === location.pathname)?.text || 'Dashboard'}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* User Profile Section */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* User Info - Desktop */}
            {user && (
              <Box sx={{
                display: { xs: 'none', md: 'flex' },
                alignItems: 'center',
                bgcolor: 'grey.50',
                borderRadius: 2,
                px: 2,
                py: 1
              }}>
                <Box sx={{ textAlign: 'right', mr: 1.5 }}>
                  <Typography variant="body2" sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                    lineHeight: 1.2
                  }}>
                    {user.full_name}
                  </Typography>
                  <Typography variant="caption" sx={{
                    color: 'text.secondary',
                    fontSize: '0.75rem'
                  }}>
                    {user.email}
                  </Typography>
                </Box>
              </Box>
            )}

            {/* Profile Avatar */}
            <Tooltip title="Account settings">
              <IconButton
                onClick={handleMenuClick}
                size="small"
                sx={{
                  p: 0.5,
                  border: '2px solid',
                  borderColor: 'divider',
                  '&:hover': {
                    borderColor: 'primary.main',
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
                aria-controls={Boolean(anchorEl) ? 'account-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={Boolean(anchorEl) ? 'true' : undefined}
              >
                <Avatar
                  src="/dr-sharma-profile.svg"
                  alt="Dr. Siddhartha Sharma"
                  sx={{
                    width: 40,
                    height: 40,
                    bgcolor: 'primary.main',
                    fontSize: '1rem',
                    fontWeight: 'bold',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  DS
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            PaperProps={{
              elevation: 2,
              sx: {
                minWidth: 200,
                borderRadius: 1,
                mt: 1,
                overflow: 'visible',
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: 'translateY(-50%) rotate(45deg)',
                  zIndex: 0,
                },
              },
            }}
          >
            {user && (
              <Box sx={{ px: 2, py: 1.5 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>{user.full_name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {user.email}
                </Typography>
              </Box>
            )}
            <Divider />
            <MenuItem onClick={() => { handleNavigation('/profile'); handleMenuClose(); }} sx={{ py: 1.5 }}>
              <ListItemIcon>
                <PersonIcon fontSize="small" color="primary" />
              </ListItemIcon>
              <ListItemText primary="Profile" />
            </MenuItem>
            <MenuItem onClick={handleLogout} sx={{ py: 1.5 }}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" color="error" />
              </ListItemIcon>
              <ListItemText primary="Logout" primaryTypographyProps={{ color: 'error.main' }} />
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { lg: currentDrawerWidth }, flexShrink: { lg: 0 } }}
        aria-label="navigation"
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', lg: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: mobileDrawerWidth,
              borderRadius: { xs: '0 16px 16px 0', lg: 0 },
              boxShadow: theme.shadows[8],
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', lg: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: currentDrawerWidth,
              transition: 'width 0.3s ease-in-out',
              overflowX: 'hidden',
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { lg: `calc(100% - ${currentDrawerWidth}px)` },
          minHeight: '100vh',
          bgcolor: 'background.default',
          display: 'flex',
          flexDirection: 'column',
          transition: 'width 0.3s ease-in-out',
        }}
      >
        <Toolbar sx={{ minHeight: appBarHeight }} />
        <Box sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3, md: 4, lg: 5 },
          maxWidth: '1800px',
          mx: 'auto',
          width: '100%',
          position: 'relative',
        }}>
          <Slide direction="up" in={true} mountOnEnter unmountOnExit>
            <Box>
              <Outlet />
            </Box>
          </Slide>
        </Box>
      </Box>
    </Box>
  );
};

export default Layout;
