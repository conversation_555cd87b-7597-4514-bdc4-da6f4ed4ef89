import axios from 'axios';
import type { AxiosResponse, AxiosError } from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  withCredentials: false, // Don't send credentials by default
});

// Add a request interceptor to add the auth token to every request
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error: AxiosError) => {
    // Handle network errors (CORS, server down, etc.)
    if (!error.response) {
      console.error('Network Error:', error.message);
      // Don't redirect on network errors to prevent infinite loops
      return Promise.reject(new Error('Network error. Please check your connection or try again later.'));
    }

    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response.status === 401) {
      localStorage.removeItem('token');
      // Only redirect if we're not already on the login page
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Authentication API
export const authAPI = {
  login: async (email: string, password: string): Promise<any> => {
    try {
      const formData = new FormData();
      formData.append('username', email);
      formData.append('password', password);

      const response = await api.post('/auth/token', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        withCredentials: false, // Don't send credentials for login
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  register: async (userData: any): Promise<any> => {
    try {
      const response = await api.post('/auth/register', userData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getCurrentUser: async (): Promise<any> => {
    try {
      const response = await api.get('/auth/me');
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Patients API
export const patientsAPI = {
  getPatients: async (params?: any): Promise<any> => {
    try {
      const response = await api.get('/patients', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getPatient: async (id: string): Promise<any> => {
    try {
      const response = await api.get(`/patients/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  createPatient: async (patientData: any): Promise<any> => {
    try {
      const response = await api.post('/patients', patientData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  updatePatient: async (id: string, patientData: any): Promise<any> => {
    try {
      const response = await api.put(`/patients/${id}`, patientData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  deletePatient: async (id: string): Promise<any> => {
    try {
      const response = await api.delete(`/patients/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Medical Images API
export const medicalImagesAPI = {
  getMedicalImages: async (params?: any): Promise<any> => {
    try {
      const response = await api.get('/medical-images', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getMedicalImage: async (id: string): Promise<any> => {
    try {
      const response = await api.get(`/medical-images/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  uploadMedicalImage: async (formData: FormData): Promise<any> => {
    try {
      const response = await api.post('/medical-images/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  updateMedicalImage: async (id: string, imageData: any): Promise<any> => {
    try {
      const response = await api.put(`/medical-images/${id}`, imageData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  deleteMedicalImage: async (id: string): Promise<any> => {
    try {
      const response = await api.delete(`/medical-images/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getMedicalImageFile: async (id: string): Promise<string> => {
    try {
      const response = await api.get(`/medical-images/file/${id}`, {
        responseType: 'blob',
      });
      return URL.createObjectURL(response.data);
    } catch (error) {
      throw error;
    }
  },
};

// Appointments API
export const appointmentsAPI = {
  getAppointments: async (params?: any): Promise<any> => {
    try {
      const response = await api.get('/appointments', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getAppointment: async (id: string): Promise<any> => {
    try {
      const response = await api.get(`/appointments/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  createAppointment: async (appointmentData: any): Promise<any> => {
    try {
      const response = await api.post('/appointments', appointmentData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  updateAppointment: async (id: string, appointmentData: any): Promise<any> => {
    try {
      const response = await api.put(`/appointments/${id}`, appointmentData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  deleteAppointment: async (id: string): Promise<any> => {
    try {
      const response = await api.delete(`/appointments/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getTodaysAppointments: async (): Promise<any[]> => {
    try {
      const today = new Date();
      const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();
      const endOfDay = new Date(today.setHours(23, 59, 59, 999)).toISOString();

      const response = await api.get('/appointments', {
        params: {
          start_date: startOfDay,
          end_date: endOfDay,
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Rehabilitation API
export const rehabilitationAPI = {
  getRehabilitationPlans: async (params?: any): Promise<any> => {
    try {
      const response = await api.get('/rehabilitation/plans', { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getRehabilitationPlan: async (id: string): Promise<any> => {
    try {
      const response = await api.get(`/rehabilitation/plans/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  createRehabilitationPlan: async (planData: any): Promise<any> => {
    try {
      const response = await api.post('/rehabilitation/plans', planData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  updateRehabilitationPlan: async (id: string, planData: any): Promise<any> => {
    try {
      const response = await api.put(`/rehabilitation/plans/${id}`, planData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  deleteRehabilitationPlan: async (id: string): Promise<any> => {
    try {
      const response = await api.delete(`/rehabilitation/plans/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getRehabilitationExercises: async (planId: string): Promise<any> => {
    try {
      const response = await api.get('/rehabilitation/exercises', {
        params: { rehabilitation_plan_id: planId },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  createRehabilitationExercise: async (exerciseData: any): Promise<any> => {
    try {
      const response = await api.post('/rehabilitation/exercises', exerciseData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  updateRehabilitationExercise: async (id: string, exerciseData: any): Promise<any> => {
    try {
      const response = await api.put(`/rehabilitation/exercises/${id}`, exerciseData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  deleteRehabilitationExercise: async (id: string): Promise<any> => {
    try {
      const response = await api.delete(`/rehabilitation/exercises/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  getRehabilitationProgress: async (planId: string): Promise<any> => {
    try {
      const response = await api.get('/rehabilitation/progress', {
        params: { rehabilitation_plan_id: planId },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  createRehabilitationProgress: async (formData: FormData): Promise<any> => {
    try {
      const response = await api.post('/rehabilitation/progress', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Reports API
export const reportsAPI = {
  generatePatientPDF: async (patientId: string, options?: any): Promise<any> => {
    try {
      const response = await api.get(`/reports/patient/${patientId}/pdf`, {
        params: options,
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Dashboard API
export const dashboardAPI = {
  getStats: async (): Promise<any> => {
    try {
      // Get dashboard statistics by making multiple API calls
      const [patients, todaysAppointments, medicalImages, rehabPlans] = await Promise.all([
        patientsAPI.getPatients({ limit: 1000 }), // Get all patients to count
        appointmentsAPI.getTodaysAppointments(),
        medicalImagesAPI.getMedicalImages({ limit: 1000 }),
        rehabilitationAPI.getRehabilitationPlans({ limit: 1000 })
      ]);

      return {
        totalPatients: patients.length,
        appointmentsToday: todaysAppointments.length,
        pendingImages: medicalImages.filter((img: any) => img.status === 'pending').length,
        activeRehabPlans: rehabPlans.filter((plan: any) => plan.status === 'Active').length
      };
    } catch (error) {
      throw error;
    }
  },
  getRecentPatients: async (limit: number = 5): Promise<any[]> => {
    try {
      const patients = await patientsAPI.getPatients({ limit });
      return patients.map((patient: any) => ({
        id: patient.id,
        name: `${patient.first_name} ${patient.last_name}`,
        age: new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear(),
        condition: patient.medical_history || 'General consultation'
      }));
    } catch (error) {
      throw error;
    }
  }
};

export default api;
