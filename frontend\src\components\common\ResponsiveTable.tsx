import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Typography,
  IconButton,
  Collapse,
  useTheme,
  useMediaQuery,
  Card,
  CardContent,
  Stack,
  Chip,
  Divider,
} from '@mui/material';
import {
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from '@mui/icons-material';

interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => string;
  hideOnMobile?: boolean;
  hideOnTablet?: boolean;
  priority?: number; // 1 = highest priority (always show), 5 = lowest priority
}

interface ResponsiveTableProps {
  columns: Column[];
  rows: any[];
  loading?: boolean;
  stickyHeader?: boolean;
  maxHeight?: number;
  onRowClick?: (row: any) => void;
  renderMobileCard?: (row: any, index: number) => React.ReactNode;
  emptyMessage?: string;
  sx?: any;
}

const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  columns,
  rows,
  loading = false,
  stickyHeader = true,
  maxHeight = 600,
  onRowClick,
  renderMobileCard,
  emptyMessage = 'No data available',
  sx = {},
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  const toggleRowExpansion = (index: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedRows(newExpanded);
  };

  // Filter columns based on screen size and priority
  const getVisibleColumns = () => {
    return columns.filter(column => {
      if (isMobile && column.hideOnMobile) return false;
      if (isTablet && column.hideOnTablet) return false;
      
      // On mobile, only show priority 1-2 columns
      if (isMobile && column.priority && column.priority > 2) return false;
      // On tablet, only show priority 1-3 columns
      if (isTablet && column.priority && column.priority > 3) return false;
      
      return true;
    });
  };

  const visibleColumns = getVisibleColumns();
  const hiddenColumns = columns.filter(col => !visibleColumns.includes(col));

  // Mobile Card View
  if (isMobile && renderMobileCard) {
    return (
      <Box sx={{ ...sx }}>
        {rows.length === 0 ? (
          <Card>
            <CardContent>
              <Typography variant="body1" color="text.secondary" textAlign="center">
                {emptyMessage}
              </Typography>
            </CardContent>
          </Card>
        ) : (
          <Stack spacing={2}>
            {rows.map((row, index) => renderMobileCard(row, index))}
          </Stack>
        )}
      </Box>
    );
  }

  // Default Mobile Card if no custom renderer provided
  const defaultMobileCard = (row: any, index: number) => (
    <Card 
      key={index}
      sx={{ 
        cursor: onRowClick ? 'pointer' : 'default',
        '&:hover': onRowClick ? {
          boxShadow: theme.shadows[4],
          transform: 'translateY(-1px)',
        } : {},
        transition: 'all 0.2s ease-in-out',
      }}
      onClick={() => onRowClick?.(row)}
    >
      <CardContent>
        <Stack spacing={2}>
          {/* Primary information */}
          <Box>
            {visibleColumns.slice(0, 2).map((column) => (
              <Typography 
                key={column.id}
                variant={column.id === visibleColumns[0]?.id ? 'h6' : 'body1'}
                sx={{ 
                  fontWeight: column.id === visibleColumns[0]?.id ? 600 : 400,
                  mb: 0.5 
                }}
              >
                {column.format ? column.format(row[column.id]) : row[column.id]}
              </Typography>
            ))}
          </Box>

          {/* Secondary information */}
          {visibleColumns.length > 2 && (
            <>
              <Divider />
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {visibleColumns.slice(2).map((column) => (
                  <Chip
                    key={column.id}
                    label={`${column.label}: ${column.format ? column.format(row[column.id]) : row[column.id]}`}
                    size="small"
                    variant="outlined"
                  />
                ))}
              </Stack>
            </>
          )}

          {/* Expandable content for hidden columns */}
          {hiddenColumns.length > 0 && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleRowExpansion(index);
                  }}
                >
                  {expandedRows.has(index) ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                </IconButton>
              </Box>
              <Collapse in={expandedRows.has(index)}>
                <Stack spacing={1}>
                  {hiddenColumns.map((column) => (
                    <Box key={column.id} sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">
                        {column.label}:
                      </Typography>
                      <Typography variant="body2">
                        {column.format ? column.format(row[column.id]) : row[column.id]}
                      </Typography>
                    </Box>
                  ))}
                </Stack>
              </Collapse>
            </>
          )}
        </Stack>
      </CardContent>
    </Card>
  );

  if (isMobile) {
    return (
      <Box sx={{ ...sx }}>
        {rows.length === 0 ? (
          <Card>
            <CardContent>
              <Typography variant="body1" color="text.secondary" textAlign="center">
                {emptyMessage}
              </Typography>
            </CardContent>
          </Card>
        ) : (
          <Stack spacing={2}>
            {rows.map((row, index) => defaultMobileCard(row, index))}
          </Stack>
        )}
      </Box>
    );
  }

  // Desktop/Tablet Table View
  return (
    <TableContainer 
      component={Paper} 
      sx={{ 
        maxHeight,
        borderRadius: 2,
        ...sx 
      }}
    >
      <Table stickyHeader={stickyHeader}>
        <TableHead>
          <TableRow>
            {visibleColumns.map((column) => (
              <TableCell
                key={column.id}
                align={column.align}
                style={{ 
                  minWidth: column.minWidth,
                  fontWeight: 600,
                  backgroundColor: theme.palette.grey[50],
                }}
              >
                {column.label}
              </TableCell>
            ))}
            {hiddenColumns.length > 0 && (
              <TableCell 
                align="center"
                style={{ 
                  width: 50,
                  backgroundColor: theme.palette.grey[50],
                }}
              >
                Details
              </TableCell>
            )}
          </TableRow>
        </TableHead>
        <TableBody>
          {rows.length === 0 ? (
            <TableRow>
              <TableCell 
                colSpan={visibleColumns.length + (hiddenColumns.length > 0 ? 1 : 0)}
                align="center"
                sx={{ py: 4 }}
              >
                <Typography variant="body1" color="text.secondary">
                  {emptyMessage}
                </Typography>
              </TableCell>
            </TableRow>
          ) : (
            rows.map((row, index) => (
              <React.Fragment key={index}>
                <TableRow
                  hover
                  onClick={() => onRowClick?.(row)}
                  sx={{ 
                    cursor: onRowClick ? 'pointer' : 'default',
                    '&:hover': {
                      backgroundColor: theme.palette.action.hover,
                    },
                  }}
                >
                  {visibleColumns.map((column) => (
                    <TableCell key={column.id} align={column.align}>
                      {column.format ? column.format(row[column.id]) : row[column.id]}
                    </TableCell>
                  ))}
                  {hiddenColumns.length > 0 && (
                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleRowExpansion(index);
                        }}
                      >
                        {expandedRows.has(index) ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                      </IconButton>
                    </TableCell>
                  )}
                </TableRow>
                {hiddenColumns.length > 0 && (
                  <TableRow>
                    <TableCell 
                      style={{ paddingBottom: 0, paddingTop: 0 }} 
                      colSpan={visibleColumns.length + 1}
                    >
                      <Collapse in={expandedRows.has(index)} timeout="auto" unmountOnExit>
                        <Box sx={{ margin: 2 }}>
                          <Typography variant="h6" gutterBottom component="div">
                            Additional Details
                          </Typography>
                          <Stack spacing={1}>
                            {hiddenColumns.map((column) => (
                              <Box key={column.id} sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                <Typography variant="body2" color="text.secondary">
                                  {column.label}:
                                </Typography>
                                <Typography variant="body2">
                                  {column.format ? column.format(row[column.id]) : row[column.id]}
                                </Typography>
                              </Box>
                            ))}
                          </Stack>
                        </Box>
                      </Collapse>
                    </TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            ))
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ResponsiveTable;
