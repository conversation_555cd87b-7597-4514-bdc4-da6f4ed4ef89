# PowerShell script to start both frontend and backend development servers

Write-Host "Starting Orthopedic EHR Development Environment" -ForegroundColor Green
Write-Host "------------------------------------------------" -ForegroundColor Green

# Start the backend server
Write-Host "Starting backend server..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; .\venv\Scripts\activate; uvicorn main:app --reload"

# Wait a moment to ensure backend starts first
Start-Sleep -Seconds 2

# Start the frontend server
Write-Host "Starting frontend server..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd frontend; npm run dev"

Write-Host "`nServers started successfully!" -ForegroundColor Green
Write-Host "Backend API: http://localhost:8000" -ForegroundColor Yellow
Write-Host "Frontend App: http://localhost:5173" -ForegroundColor Yellow
Write-Host "API Documentation: http://localhost:8000/docs" -ForegroundColor Yellow
Write-Host "`nPress Ctrl+C in each terminal window to stop the servers." -ForegroundColor Gray
