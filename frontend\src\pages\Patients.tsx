import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFocusManagement, useKeyboardNavigation } from '../hooks/useFocusManagement';
import { patientsAPI } from '../services/api';
import {
  Box,
  Button,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Fade,
  Slide,
  Zoom,
  Stack,
  useTheme,
  alpha,
  Skeleton,
  Fab,
  Badge,
  CardActions,
  CardHeader,
  Chip,
  Avatar,
  Tooltip,
  TableSortLabel,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemIcon,
} from '@mui/material';

// Define the SelectChangeEvent type
type SelectChangeEvent = {
  target: {
    value: string;
  };
};
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  People as PeopleIcon,
  FilterList as FilterListIcon,
  Sort as SortIcon,
  MoreVert as MoreVertIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  MedicalServices as MedicalIcon,
  Warning as WarningIcon,
  Person as PersonIcon,
  Cake as CakeIcon,
  Wc as GenderIcon,
  Refresh as RefreshIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import UniversalFilter, { type FilterField } from '../components/common/UniversalFilter';
import { useUniversalFilter, filterFunctions, sortFunctions } from '../hooks/useUniversalFilter';

// Patient interface - matches backend API
interface Patient {
  id: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  contact_number: string;
  email: string;
  address?: string;
  medical_history?: string;
  allergies?: string;
  created_at: string;
  updated_at: string;
}

const Patients = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [selectedPatients, setSelectedPatients] = useState<string[]>([]);
  const [hoveredPatient, setHoveredPatient] = useState<string | null>(null);

  // Universal Filter Configuration
  const filterFields: FilterField[] = [
    {
      key: 'gender',
      label: 'Gender',
      type: 'select',
      options: [
        { value: 'Male', label: 'Male', icon: <GenderIcon /> },
        { value: 'Female', label: 'Female', icon: <GenderIcon /> },
      ],
      width: 4,
    },
    {
      key: 'ageRange',
      label: 'Age Range',
      type: 'text',
      placeholder: 'e.g., 25-65',
      width: 4,
    },
  ];

  const {
    filteredData: filteredPatients,
    searchValue,
    setSearchValue,
    filterValues,
    setFilterValue,
    sortValue,
    setSortValue,
    totalCount,
    filteredCount,
    resetAll,
  } = useUniversalFilter({
    data: patients,
    searchFields: ['first_name', 'last_name', 'email', 'contact_number'],
    filterConfigs: [
      {
        key: 'gender',
        defaultValue: '',
        filterFunction: (patient, value) => filterFunctions.exact(patient, value, 'gender'),
      },
      {
        key: 'ageRange',
        defaultValue: '',
        filterFunction: (patient, value) => {
          if (!value) return true;
          const [min, max] = value.split('-').map((v: string) => parseInt(v.trim()));
          if (isNaN(min) || isNaN(max)) return true;
          const age = new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear();
          return age >= min && age <= max;
        },
      },
    ],
    defaultSort: 'first_name_asc',
    sortConfigs: {
      'first_name_asc': { key: 'first_name', direction: 'asc', sortFunction: sortFunctions.string },
      'first_name_desc': { key: 'first_name', direction: 'desc', sortFunction: sortFunctions.string },
      'last_name_asc': { key: 'last_name', direction: 'asc', sortFunction: sortFunctions.string },
      'last_name_desc': { key: 'last_name', direction: 'desc', sortFunction: sortFunctions.string },
      'created_at_desc': { key: 'created_at', direction: 'desc', sortFunction: sortFunctions.date },
      'created_at_asc': { key: 'created_at', direction: 'asc', sortFunction: sortFunctions.date },
    },
  });

  const sortOptions = [
    { value: 'first_name_asc', label: 'First Name (A-Z)' },
    { value: 'first_name_desc', label: 'First Name (Z-A)' },
    { value: 'last_name_asc', label: 'Last Name (A-Z)' },
    { value: 'last_name_desc', label: 'Last Name (Z-A)' },
    { value: 'created_at_desc', label: 'Newest First' },
    { value: 'created_at_asc', label: 'Oldest First' },
  ];

  // Form state - matches backend API format
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    date_of_birth: '',
    gender: '',
    contact_number: '',
    email: '',
    address: '',
    medical_history: '',
    allergies: '',
  });

  // Focus management for accessibility
  const dialogRef = useFocusManagement(openDialog);
  useKeyboardNavigation(openDialog, () => setOpenDialog(false));

  // Fetch patients from API
  useEffect(() => {
    const fetchPatients = async () => {
      setLoading(true);
      setError(null);

      try {
        const patientsData = await patientsAPI.getPatients();
        setPatients(patientsData);
      } catch (err) {
        console.error('Error fetching patients:', err);
        setError('Failed to load patients. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, []);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleAddPatient = () => {
    setSelectedPatient(null);
    setFormData({
      first_name: '',
      last_name: '',
      date_of_birth: '',
      gender: '',
      contact_number: '',
      email: '',
      address: '',
      medical_history: '',
      allergies: '',
    });
    setOpenDialog(true);
  };

  const handleEditPatient = (patient: Patient) => {
    setSelectedPatient(patient);
    setFormData({
      first_name: patient.first_name,
      last_name: patient.last_name,
      date_of_birth: patient.date_of_birth,
      gender: patient.gender,
      contact_number: patient.contact_number,
      email: patient.email,
      address: patient.address || '',
      medical_history: patient.medical_history || '',
      allergies: patient.allergies || '',
    });
    setOpenDialog(true);
  };

  const handleViewPatient = (id: string) => {
    navigate(`/patients/${id}`);
  };

  const handleDeletePatient = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this patient?')) {
      try {
        await patientsAPI.deletePatient(id);
        // Remove from local state
        setPatients(patients.filter((patient) => patient.id !== id));
      } catch (error) {
        console.error('Error deleting patient:', error);
        setError('Failed to delete patient. Please try again.');
      }
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedPatient(null);
  };

  const handleSavePatient = async () => {
    try {
      setError(null);

      // Basic validation
      if (!formData.first_name || !formData.last_name || !formData.email || !formData.contact_number) {
        setError('Please fill in all required fields.');
        return;
      }

      if (selectedPatient) {
        // Update existing patient
        const updatedPatient = await patientsAPI.updatePatient(selectedPatient.id, formData);
        // Update local state
        setPatients(patients.map(p =>
          p.id === selectedPatient.id ? updatedPatient : p
        ));
      } else {
        // Create new patient
        const newPatient = await patientsAPI.createPatient(formData);
        // Add to local state
        setPatients([...patients, newPatient]);
      }

      handleCloseDialog();
    } catch (error) {
      console.error('Error saving patient:', error);
      setError('Failed to save patient. Please try again.');
    }
  };

  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Box>
      {/* Enhanced Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems={{ xs: 'flex-start', sm: 'center' }} spacing={2}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'primary.main' }}>
              Patient Management
            </Typography>
            <Typography variant="body1" sx={{ color: 'text.secondary', mb: 2 }}>
              Manage patient records, view details, and track medical history
            </Typography>
            <Stack direction="row" spacing={2} alignItems="center">
              <Chip
                icon={<PeopleIcon />}
                label={`${patients.length} Total Patients`}
                color="primary"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<FilterListIcon />}
                label={`${filteredPatients.length} Filtered`}
                color="secondary"
                variant="outlined"
                size="small"
              />
            </Stack>
          </Box>
          <Stack direction="row" spacing={1}>
            <Tooltip title="Refresh Data">
              <IconButton
                onClick={() => window.location.reload()}
                sx={{
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                  '&:hover': {
                    bgcolor: 'action.hover',
                  }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export Data">
              <IconButton
                sx={{
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                  '&:hover': {
                    bgcolor: 'action.hover',
                  }
                }}
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddPatient}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1.5,
                boxShadow: theme.shadows[3],
                '&:hover': {
                  boxShadow: theme.shadows[6],
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              Add Patient
            </Button>
          </Stack>
        </Stack>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3, borderRadius: 1 }}
          action={
            <Button color="inherit" size="small" onClick={() => window.location.reload()}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Universal Filter Component */}
      <UniversalFilter
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        searchPlaceholder="Search by name, email, or phone number..."
        filters={filterFields}
        filterValues={filterValues}
        onFilterChange={setFilterValue}
        sortOptions={sortOptions}
        sortValue={sortValue}
        onSortChange={setSortValue}
        totalCount={totalCount}
        filteredCount={filteredCount}
        title="Patient Search & Filter"
        collapsible={true}
        defaultExpanded={false}
        showResultCount={true}
        onClearAll={resetAll}
        customActions={
          <Tooltip title={viewMode === 'table' ? 'Switch to Cards' : 'Switch to Table'}>
            <IconButton
              onClick={() => setViewMode(viewMode === 'table' ? 'cards' : 'table')}
              sx={{
                bgcolor: 'action.hover',
                '&:hover': {
                  bgcolor: 'action.selected',
                }
              }}
            >
              {viewMode === 'table' ? <ViewModuleIcon /> : <ViewListIcon />}
            </IconButton>
          </Tooltip>
        }
      />

      <Paper elevation={0} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'background.default' }}>
                <TableCell sx={{ fontWeight: 'bold' }}>Name</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Date of Birth</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Gender</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Contact</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 5 }}>
                    <CircularProgress size={40} />
                    <Typography variant="body1" sx={{ mt: 2, color: 'text.secondary' }}>
                      Loading patients...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : filteredPatients.length > 0 ? (
                filteredPatients
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((patient) => (
                    <TableRow
                      key={patient.id}
                      hover
                      sx={{
                        '&:hover': {
                          bgcolor: 'action.hover',
                          cursor: 'pointer'
                        }
                      }}
                      onClick={() => handleViewPatient(patient.id)}
                    >
                      <TableCell sx={{ fontWeight: 500 }}>{`${patient.first_name} ${patient.last_name}`}</TableCell>
                      <TableCell>{new Date(patient.date_of_birth).toLocaleDateString()}</TableCell>
                      <TableCell>{patient.gender}</TableCell>
                      <TableCell>{patient.contact_number}</TableCell>
                      <TableCell>{patient.email}</TableCell>
                      <TableCell align="center">
                        <IconButton
                          color="primary"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewPatient(patient.id);
                          }}
                          size="small"
                          sx={{ mx: 0.5 }}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          color="primary"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditPatient(patient);
                          }}
                          size="small"
                          sx={{ mx: 0.5 }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeletePatient(patient.id);
                          }}
                          size="small"
                          sx={{ mx: 0.5 }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 8 }}>
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      p: 3
                    }}>
                      <PeopleIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        No patients found
                      </Typography>
                      <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3, maxWidth: 500 }}>
                        {searchValue || Object.values(filterValues).some(v => v !== '' && v !== null && v !== undefined) ?
                          'Try adjusting your search or filter criteria.' :
                          'Get started by adding your first patient to the system.'}
                      </Typography>
                      <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={handleAddPatient}
                      >
                        Add Patient
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {filteredPatients.length > 0 && (
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredPatients.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        )}
      </Paper>

      {/* Patient Form Dialog - In a real app, this would be a separate component */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        aria-labelledby="patient-dialog-title"
        aria-describedby="patient-dialog-description"
        disableRestoreFocus={false}
        ref={dialogRef}
        PaperProps={{
          elevation: 2,
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }} id="patient-dialog-title">
          <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
            {selectedPatient ? 'Edit Patient' : 'Add New Patient'}
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mt: 0.5 }}
            id="patient-dialog-description"
          >
            {selectedPatient
              ? 'Update the patient information below'
              : 'Fill in the patient information below'}
          </Typography>
        </DialogTitle>
        <Divider />
        <DialogContent sx={{ py: 3 }}>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.first_name}
                onChange={(e) => handleFormChange('first_name', e.target.value)}
                required
                InputProps={{ sx: { borderRadius: 1 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.last_name}
                onChange={(e) => handleFormChange('last_name', e.target.value)}
                required
                InputProps={{ sx: { borderRadius: 1 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Date of Birth"
                  value={formData.date_of_birth ? new Date(formData.date_of_birth) : null}
                  onChange={(date) => handleFormChange('date_of_birth', date ? date.toISOString().split('T')[0] : '')}
                  sx={{ width: '100%' }}
                  slotProps={{
                    textField: {
                      required: true,
                      InputProps: { sx: { borderRadius: 1 } }
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Gender</InputLabel>
                <Select
                  label="Gender"
                  value={formData.gender}
                  onChange={(e) => handleFormChange('gender', e.target.value)}
                  sx={{ borderRadius: 1 }}
                >
                  <MenuItem value="Male">Male</MenuItem>
                  <MenuItem value="Female">Female</MenuItem>
                  <MenuItem value="Other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Contact Number"
                value={formData.contact_number}
                onChange={(e) => handleFormChange('contact_number', e.target.value)}
                required
                InputProps={{ sx: { borderRadius: 1 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleFormChange('email', e.target.value)}
                required
                InputProps={{ sx: { borderRadius: 1 } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                multiline
                rows={2}
                value={formData.address}
                onChange={(e) => handleFormChange('address', e.target.value)}
                InputProps={{ sx: { borderRadius: 1 } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Medical History"
                multiline
                rows={3}
                value={formData.medical_history}
                onChange={(e) => handleFormChange('medical_history', e.target.value)}
                InputProps={{ sx: { borderRadius: 1 } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Allergies"
                multiline
                rows={2}
                value={formData.allergies}
                onChange={(e) => handleFormChange('allergies', e.target.value)}
                InputProps={{ sx: { borderRadius: 1 } }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <Divider />
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            sx={{ borderRadius: 1, px: 2 }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSavePatient}
            sx={{
              borderRadius: 1,
              px: 3,
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            }}
          >
            {selectedPatient ? 'Update Patient' : 'Add Patient'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Patients;
