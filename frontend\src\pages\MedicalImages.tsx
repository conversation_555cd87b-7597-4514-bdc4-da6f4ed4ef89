import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  CircularProgress,
  Alert,
  Stack,
  useTheme,
  alpha,
  Fade,
  Tooltip,
  Fab,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';

import {
  Add as AddIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Image as ImageIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  CloudUpload as CloudUploadIcon,
  MedicalServices as MedicalServicesIcon,
  Scanner as ScannerIcon,
  AttachFile as AttachFileIcon,
  CheckCircle as CheckCircleIcon,
  Error as <PERSON>rrorIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { medicalImagesAPI, patientsAPI } from '../services/api';
import MedicalImageViewer from '../components/MedicalImageViewer';
import UniversalFilter, { type FilterField } from '../components/common/UniversalFilter';
import { useUniversalFilter, filterFunctions, sortFunctions } from '../hooks/useUniversalFilter';

// Define the SelectChangeEvent type
type SelectChangeEvent = {
  target: {
    value: string;
  };
};

// Interface for medical images
interface MedicalImage {
  id: string;
  patient_id: string;
  patientName: string;
  image_type: string;
  body_part: string;
  taken_date: string;
  description?: string;
  file_path: string;
  file_name: string;
  file_type: string;
  created_at: string;
  updated_at: string;
}

// Interface for patients
interface Patient {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

// Interface for file upload
interface FileUpload {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

const MedicalImages = () => {
  const navigate = useNavigate();
  const theme = useTheme();

  // State management
  const [medicalImages, setMedicalImages] = useState<MedicalImage[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [selectedImage, setSelectedImage] = useState<MedicalImage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);

  // Universal Filter Configuration
  const filterFields: FilterField[] = [
    {
      key: 'patient',
      label: 'Patient',
      type: 'select',
      options: patients.map(patient => ({
        value: patient.id,
        label: `${patient.first_name} ${patient.last_name}`,
        avatar: `${patient.first_name[0]}${patient.last_name[0]}`,
      })),
      width: 4,
    },
    {
      key: 'imageType',
      label: 'Image Type',
      type: 'select',
      options: Array.from(new Set(medicalImages.map(img => img.image_type))).map(type => ({
        value: type,
        label: type,
        icon: <ImageIcon />,
      })),
      width: 4,
    },
    {
      key: 'bodyPart',
      label: 'Body Part',
      type: 'select',
      options: Array.from(new Set(medicalImages.map(img => img.body_part))).map(part => ({
        value: part,
        label: part,
        icon: <MedicalServicesIcon />,
      })),
      width: 4,
    },
  ];

  const {
    filteredData: filteredImages,
    searchValue,
    setSearchValue,
    filterValues,
    setFilterValue,
    sortValue,
    setSortValue,
    totalCount,
    filteredCount,
    resetAll,
  } = useUniversalFilter({
    data: medicalImages,
    searchFields: ['patientName', 'image_type', 'body_part', 'description'],
    filterConfigs: [
      {
        key: 'patient',
        defaultValue: '',
        filterFunction: (image, value) => filterFunctions.exact(image, value, 'patient_id'),
      },
      {
        key: 'imageType',
        defaultValue: '',
        filterFunction: (image, value) => filterFunctions.exact(image, value, 'image_type'),
      },
      {
        key: 'bodyPart',
        defaultValue: '',
        filterFunction: (image, value) => filterFunctions.exact(image, value, 'body_part'),
      },
    ],
    defaultSort: 'date_desc',
    sortConfigs: {
      'date_desc': { key: 'taken_date', direction: 'desc', sortFunction: sortFunctions.date },
      'date_asc': { key: 'taken_date', direction: 'asc', sortFunction: sortFunctions.date },
      'patient_asc': { key: 'patientName', direction: 'asc', sortFunction: sortFunctions.string },
      'patient_desc': { key: 'patientName', direction: 'desc', sortFunction: sortFunctions.string },
      'type_asc': { key: 'image_type', direction: 'asc', sortFunction: sortFunctions.string },
    },
  });

  const sortOptions = [
    { value: 'date_desc', label: 'Date (Newest First)' },
    { value: 'date_asc', label: 'Date (Oldest First)' },
    { value: 'patient_asc', label: 'Patient (A-Z)' },
    { value: 'patient_desc', label: 'Patient (Z-A)' },
    { value: 'type_asc', label: 'Image Type (A-Z)' },
  ];

  // Upload form state
  const [selectedPatient, setSelectedPatient] = useState('');
  const [selectedImageType, setSelectedImageType] = useState('');
  const [selectedBodyPart, setSelectedBodyPart] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [description, setDescription] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<FileUpload[]>([]);

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch patients and medical images in parallel
      const [patientsData, imagesData] = await Promise.all([
        patientsAPI.getPatients(),
        medicalImagesAPI.getMedicalImages()
      ]);

      setPatients(patientsData);

      // Format images with patient names
      const patientMap = new Map(
        patientsData.map((p: Patient) => [p.id, `${p.first_name} ${p.last_name}`])
      );

      const formattedImages = imagesData.map((img: any) => ({
        ...img,
        patientName: patientMap.get(img.patient_id) || 'Unknown Patient',
      }));

      setMedicalImages(formattedImages);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };



  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const newFiles: FileUpload[] = files.map((file) => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
      progress: 0,
      status: 'pending',
    }));
    setSelectedFiles(prev => [...prev, ...newFiles]);
  };

  // Remove file from upload list
  const removeFile = (fileId: string) => {
    setSelectedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedPatient || !selectedImageType || !selectedBodyPart || !selectedDate || selectedFiles.length === 0) {
      setError('Please fill in all required fields and select at least one file.');
      return;
    }

    setUploading(true);
    setError(null);

    try {
      // Upload files one by one
      for (const fileUpload of selectedFiles) {
        if (fileUpload.status === 'success') continue;

        // Update file status to uploading
        setSelectedFiles(prev =>
          prev.map(f => f.id === fileUpload.id ? { ...f, status: 'uploading' as const } : f)
        );

        try {
          const formData = new FormData();
          formData.append('file', fileUpload.file);
          formData.append('patient_id', selectedPatient);
          formData.append('image_type', selectedImageType);
          formData.append('body_part', selectedBodyPart);
          formData.append('taken_date', selectedDate.toISOString());
          formData.append('description', description);

          await medicalImagesAPI.uploadMedicalImage(formData);

          // Update file status to success
          setSelectedFiles(prev =>
            prev.map(f => f.id === fileUpload.id ? { ...f, status: 'success' as const, progress: 100 } : f)
          );
        } catch (err) {
          // Update file status to error
          setSelectedFiles(prev =>
            prev.map(f => f.id === fileUpload.id ? {
              ...f,
              status: 'error' as const,
              error: 'Upload failed'
            } : f)
          );
        }
      }

      // Check if all uploads were successful
      const allSuccessful = selectedFiles.every(f => f.status === 'success');

      if (allSuccessful) {
        // Refresh data and close dialog
        await fetchData();
        handleCloseDialog();
      }
    } catch (err) {
      setError('Upload failed. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setSelectedPatient('');
    setSelectedImageType('');
    setSelectedBodyPart('');
    setSelectedDate(new Date());
    setDescription('');
    setSelectedFiles([]);
    setError(null);
  };

  // Handle dialog close
  const handleCloseDialog = () => {
    setOpenDialog(false);
    resetForm();
  };

  // Handle add image
  const handleAddImage = () => {
    resetForm();
    setOpenDialog(true);
  };

  // Handle view image
  const handleViewImage = (image: MedicalImage) => {
    setSelectedImage(image);
    setOpenViewDialog(true);
  };

  // Handle delete image
  const handleDeleteImage = async (id: string) => {
    try {
      await medicalImagesAPI.deleteMedicalImage(id);
      await fetchData();
    } catch (err) {
      setError('Failed to delete image. Please try again.');
    }
  };



  return (
    <Box>
      {/* Enhanced Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`,
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems={{ xs: 'flex-start', sm: 'center' }} spacing={2}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'info.main' }}>
              Medical Imaging Center
            </Typography>
            <Typography variant="body1" sx={{ color: 'text.secondary', mb: 2 }}>
              Manage DICOM images, X-rays, MRIs, CT scans, and other medical imaging data
            </Typography>
            <Stack direction="row" spacing={2} alignItems="center">
              <Chip
                icon={<ImageIcon />}
                label={`${medicalImages.length} Total Images`}
                color="info"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<ScannerIcon />}
                label={`${medicalImages.filter(img => img.image_type === 'X-Ray').length} X-Rays`}
                color="primary"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<MedicalServicesIcon />}
                label={`${medicalImages.filter(img => img.image_type === 'MRI').length} MRIs`}
                color="secondary"
                variant="outlined"
                size="small"
              />
            </Stack>
          </Box>
          <Stack direction="row" spacing={1}>
            <Tooltip title="Refresh Data">
              <IconButton
                onClick={fetchData}
                sx={{
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                  '&:hover': {
                    bgcolor: 'action.hover',
                  }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<CloudUploadIcon />}
              onClick={handleAddImage}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1.5,
                boxShadow: theme.shadows[3],
                '&:hover': {
                  boxShadow: theme.shadows[6],
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              Upload Images
            </Button>
          </Stack>
        </Stack>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3, borderRadius: 1 }}
          action={
            <Button color="inherit" size="small" onClick={() => setError(null)}>
              Dismiss
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Universal Filter Component */}
      <UniversalFilter
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        searchPlaceholder="Search by patient name, image type, body part, or description..."
        filters={filterFields}
        filterValues={filterValues}
        onFilterChange={setFilterValue}
        sortOptions={sortOptions}
        sortValue={sortValue}
        onSortChange={setSortValue}
        totalCount={totalCount}
        filteredCount={filteredCount}
        title="Medical Image Search & Filter"
        collapsible={true}
        defaultExpanded={false}
        showResultCount={true}
        onClearAll={resetAll}
      />

      {/* Images Table */}
      <Paper elevation={0} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'background.default' }}>
                <TableCell sx={{ fontWeight: 'bold' }}>Patient</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Image Type</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Body Part</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Date</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 5 }}>
                    <CircularProgress size={40} />
                    <Typography variant="body1" sx={{ mt: 2, color: 'text.secondary' }}>
                      Loading medical images...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : filteredImages.length > 0 ? (
                filteredImages
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((image) => (
                    <TableRow
                      key={image.id}
                      hover
                      sx={{
                        '&:hover': {
                          bgcolor: 'action.hover',
                          cursor: 'pointer'
                        }
                      }}
                      onClick={() => handleViewImage(image)}
                    >
                      <TableCell sx={{ fontWeight: 500 }}>{image.patientName}</TableCell>
                      <TableCell>{image.image_type}</TableCell>
                      <TableCell>{image.body_part}</TableCell>
                      <TableCell>{new Date(image.taken_date).toLocaleDateString()}</TableCell>
                      <TableCell>{image.description || 'N/A'}</TableCell>
                      <TableCell align="center">
                        <IconButton
                          color="primary"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewImage(image);
                          }}
                          size="small"
                          sx={{ mx: 0.5 }}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteImage(image.id);
                          }}
                          size="small"
                          sx={{ mx: 0.5 }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 8 }}>
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      p: 3
                    }}>
                      <ImageIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        No medical images found
                      </Typography>
                      <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3, maxWidth: 500 }}>
                        {patientFilter || imageTypeFilter || bodyPartFilter ?
                          'Try adjusting your search or filter criteria.' :
                          'Get started by uploading your first medical image.'}
                      </Typography>
                      <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={handleAddImage}
                      >
                        Upload Images
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {filteredImages.length > 0 && (
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredImages.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(event, newPage) => setPage(newPage)}
            onRowsPerPageChange={(event) => {
              setRowsPerPage(parseInt(event.target.value, 10));
              setPage(0);
            }}
          />
        )}
      </Paper>

      {/* Upload Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        disableRestoreFocus
        PaperProps={{
          elevation: 2,
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
            Upload Medical Images
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            Select multiple files and fill in the image information
          </Typography>
        </DialogTitle>
        <Divider />
        <DialogContent sx={{ py: 3 }}>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Patient</InputLabel>
                <Select
                  value={selectedPatient}
                  label="Patient"
                  onChange={(e: SelectChangeEvent) => setSelectedPatient(e.target.value)}
                  sx={{ borderRadius: 1 }}
                >
                  {patients.map((patient) => (
                    <MenuItem key={patient.id} value={patient.id}>
                      {patient.first_name} {patient.last_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Image Type</InputLabel>
                <Select
                  value={selectedImageType}
                  label="Image Type"
                  onChange={(e: SelectChangeEvent) => setSelectedImageType(e.target.value)}
                  sx={{ borderRadius: 1 }}
                >
                  <MenuItem value="X-Ray">X-Ray</MenuItem>
                  <MenuItem value="MRI">MRI</MenuItem>
                  <MenuItem value="CT Scan">CT Scan</MenuItem>
                  <MenuItem value="Ultrasound">Ultrasound</MenuItem>
                  <MenuItem value="DICOM">DICOM</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Body Part</InputLabel>
                <Select
                  value={selectedBodyPart}
                  label="Body Part"
                  onChange={(e: SelectChangeEvent) => setSelectedBodyPart(e.target.value)}
                  sx={{ borderRadius: 1 }}
                >
                  <MenuItem value="Knee">Knee</MenuItem>
                  <MenuItem value="Hip">Hip</MenuItem>
                  <MenuItem value="Shoulder">Shoulder</MenuItem>
                  <MenuItem value="Spine">Spine</MenuItem>
                  <MenuItem value="Ankle">Ankle</MenuItem>
                  <MenuItem value="Elbow">Elbow</MenuItem>
                  <MenuItem value="Wrist">Wrist</MenuItem>
                  <MenuItem value="Hand">Hand</MenuItem>
                  <MenuItem value="Foot">Foot</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Date Taken"
                  value={selectedDate}
                  onChange={(newValue) => setSelectedDate(newValue)}
                  sx={{ width: '100%' }}
                  slotProps={{
                    textField: {
                      required: true,
                      InputProps: { sx: { borderRadius: 1 } }
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                InputProps={{ sx: { borderRadius: 1 } }}
              />
            </Grid>
            <Grid item xs={12}>
              <Box sx={{
                border: '2px dashed',
                borderColor: selectedFiles.length > 0 ? 'primary.main' : 'divider',
                borderRadius: 1,
                p: 3,
                textAlign: 'center',
                bgcolor: 'background.default',
                transition: 'border-color 0.2s ease-in-out'
              }}>
                <CloudUploadIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 1 }} />
                <Typography variant="subtitle1" gutterBottom>
                  Drag and drop your image files here
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Supported formats: DICOM (.dcm), JPG, PNG, PDF
                </Typography>
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<AttachFileIcon />}
                  sx={{ borderRadius: 1 }}
                >
                  Select Files
                  <input
                    type="file"
                    hidden
                    multiple
                    accept=".dcm,.jpg,.jpeg,.png,.pdf"
                    onChange={handleFileSelect}
                  />
                </Button>
              </Box>
            </Grid>
            {selectedFiles.length > 0 && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                  Selected Files ({selectedFiles.length})
                </Typography>
                <List sx={{ bgcolor: 'background.default', borderRadius: 1 }}>
                  {selectedFiles.map((fileUpload) => (
                    <ListItem key={fileUpload.id} sx={{ py: 1 }}>
                      <ListItemIcon>
                        {fileUpload.status === 'success' ? (
                          <CheckCircleIcon color="success" />
                        ) : fileUpload.status === 'error' ? (
                          <ErrorIcon color="error" />
                        ) : fileUpload.status === 'uploading' ? (
                          <CircularProgress size={24} />
                        ) : (
                          <AttachFileIcon />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={fileUpload.file.name}
                        secondary={
                          <React.Fragment>
                            <Typography variant="caption" color="text.secondary" component="span" display="block">
                              {(fileUpload.file.size / 1024 / 1024).toFixed(2)} MB
                            </Typography>
                            {fileUpload.status === 'uploading' && (
                              <LinearProgress
                                variant="determinate"
                                value={fileUpload.progress}
                                sx={{ mt: 1 }}
                              />
                            )}
                            {fileUpload.error && (
                              <Typography variant="caption" color="error" component="span" display="block">
                                {fileUpload.error}
                              </Typography>
                            )}
                          </React.Fragment>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => removeFile(fileUpload.id)}
                          disabled={fileUpload.status === 'uploading'}
                        >
                          <CloseIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <Divider />
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            sx={{ borderRadius: 1, px: 2 }}
            disabled={uploading}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={uploading || selectedFiles.length === 0}
            sx={{
              borderRadius: 1,
              px: 3,
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            }}
          >
            {uploading ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} color="inherit" />
                Uploading...
              </>
            ) : (
              'Upload Images'
            )}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Dialog */}
      <Dialog
        open={openViewDialog}
        onClose={() => setOpenViewDialog(false)}
        maxWidth="md"
        fullWidth
        disableRestoreFocus
        PaperProps={{
          elevation: 2,
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
            {selectedImage ? `${selectedImage.image_type} - ${selectedImage.body_part}` : 'View Image'}
          </Typography>
          {selectedImage && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              Patient: {selectedImage.patientName}
            </Typography>
          )}
        </DialogTitle>
        <Divider />
        <DialogContent sx={{ py: 3 }}>
          <Box sx={{ mb: 3 }}>
            {selectedImage ? (
              <MedicalImageViewer
                imageId={selectedImage.id}
                imageType={selectedImage.image_type}
                title={`${selectedImage.image_type} - ${selectedImage.body_part}`}
              />
            ) : (
              <Paper
                elevation={0}
                sx={{
                  height: 400,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'background.default',
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 2,
                }}
              >
                <Box sx={{ textAlign: 'center' }}>
                  <ImageIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No Image Selected
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Please select an image to view.
                  </Typography>
                </Box>
              </Paper>
            )}
          </Box>
          {selectedImage && (
            <Card elevation={0} sx={{ borderRadius: 2, bgcolor: 'background.default' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 500 }}>
                  Image Details
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Patient
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {selectedImage.patientName}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Image Type
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {selectedImage.image_type}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Body Part
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {selectedImage.body_part}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Date Taken
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {new Date(selectedImage.taken_date).toLocaleDateString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Description
                    </Typography>
                    <Typography variant="body1">
                      {selectedImage.description || 'No description provided.'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}
        </DialogContent>
        <Divider />
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={() => setOpenViewDialog(false)}
            variant="outlined"
            sx={{ borderRadius: 1, px: 2 }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="upload images"
        onClick={handleAddImage}
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          boxShadow: theme.shadows[6],
          '&:hover': {
            boxShadow: theme.shadows[12],
            transform: 'scale(1.1)',
          },
          transition: 'all 0.2s ease-in-out'
        }}
      >
        <CloudUploadIcon />
      </Fab>
    </Box>
  );
};

export default MedicalImages;
