import { useEffect, useRef } from 'react';

/**
 * Custom hook to manage focus in dialogs and modals
 * Helps prevent accessibility warnings related to aria-hidden
 */
export const useFocusManagement = (isOpen: boolean) => {
  const previousActiveElement = useRef<HTMLElement | null>(null);
  const dialogRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      previousActiveElement.current = document.activeElement as HTMLElement;
      
      // Focus the dialog when it opens
      setTimeout(() => {
        if (dialogRef.current) {
          const firstFocusableElement = dialogRef.current.querySelector(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          ) as HTMLElement;
          
          if (firstFocusableElement) {
            firstFocusableElement.focus();
          } else {
            // If no focusable element found, focus the dialog itself
            dialogRef.current.focus();
          }
        }
      }, 100);
    } else {
      // Restore focus to the previously focused element when dialog closes
      if (previousActiveElement.current) {
        setTimeout(() => {
          previousActiveElement.current?.focus();
        }, 100);
      }
    }
  }, [isOpen]);

  return dialogRef;
};

/**
 * Custom hook to handle keyboard navigation in dialogs
 */
export const useKeyboardNavigation = (isOpen: boolean, onClose: () => void) => {
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Close dialog on Escape key
      if (event.key === 'Escape') {
        onClose();
        return;
      }

      // Trap focus within the dialog
      if (event.key === 'Tab') {
        const dialog = document.querySelector('[role="dialog"]') as HTMLElement;
        if (!dialog) return;

        const focusableElements = dialog.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        if (event.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);
};
