import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { patientsAPI, medicalImagesAPI, appointmentsAPI, rehabilitationAPI, reportsAPI } from '../services/api';
import {
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  Grid,
  IconButton,
  Paper,
  Tab,
  Tabs,
  Typography,
  List,
  ListItem,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Edit as EditIcon,
  ArrowBack as ArrowBackIcon,
  Download as DownloadIcon,
  Image as ImageIcon,
  Event as EventIcon,
  FitnessCenter as FitnessCenterIcon,
} from '@mui/icons-material';

// Mock data for demonstration
interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  contactNumber: string;
  email: string;
  address?: string;
  medicalHistory?: string;
  allergies?: string;
}

interface MedicalImage {
  id: string;
  imageType: string;
  bodyPart: string;
  takenDate: string;
  description?: string;
  filePath: string;
}

interface Appointment {
  id: string;
  title: string;
  appointmentDate: string;
  status: string;
  notes?: string;
}

interface RehabilitationPlan {
  id: string;
  title: string;
  startDate: string;
  endDate?: string;
  status: string;
  description?: string;
  exercises: {
    id: string;
    name: string;
    frequency: string;
    duration: string;
  }[];
}

const mockPatient: Patient = {
  id: '1',
  firstName: 'John',
  lastName: 'Smith',
  dateOfBirth: '1975-05-15',
  gender: 'Male',
  contactNumber: '************',
  email: '<EMAIL>',
  address: '123 Main St, Anytown, USA',
  medicalHistory: 'Knee replacement surgery in 2020. History of osteoarthritis in both knees. Physical therapy for 6 months post-surgery.',
  allergies: 'Penicillin',
};

const mockMedicalImages: MedicalImage[] = [
  {
    id: '1',
    imageType: 'X-Ray',
    bodyPart: 'Knee',
    takenDate: '2023-01-15',
    description: 'Post-operative follow-up',
    filePath: '/images/knee-xray.jpg',
  },
  {
    id: '2',
    imageType: 'MRI',
    bodyPart: 'Knee',
    takenDate: '2022-11-20',
    description: 'Pre-operative assessment',
    filePath: '/images/knee-mri.jpg',
  },
];

const mockAppointments: Appointment[] = [
  {
    id: '1',
    title: 'Follow-up Consultation',
    appointmentDate: '2023-02-10T09:00:00',
    status: 'Completed',
    notes: 'Patient reports improved mobility. Continue with current rehabilitation plan.',
  },
  {
    id: '2',
    title: 'Pain Management Review',
    appointmentDate: '2023-03-15T14:30:00',
    status: 'Scheduled',
    notes: 'Discuss effectiveness of current pain management regimen.',
  },
];

const mockRehabPlan: RehabilitationPlan = {
  id: '1',
  title: 'Post Knee Replacement Rehabilitation',
  startDate: '2023-01-20',
  endDate: '2023-04-20',
  status: 'Active',
  description: 'Comprehensive rehabilitation program to restore knee function and mobility following total knee replacement surgery.',
  exercises: [
    {
      id: '1',
      name: 'Straight Leg Raises',
      frequency: '3 times per day',
      duration: '10 repetitions',
    },
    {
      id: '2',
      name: 'Knee Flexion Exercises',
      frequency: '2 times per day',
      duration: '15 repetitions',
    },
    {
      id: '3',
      name: 'Walking',
      frequency: 'Daily',
      duration: '15-30 minutes',
    },
  ],
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`patient-tabpanel-${index}`}
      aria-labelledby={`patient-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const PatientDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [patient, setPatient] = useState<Patient | null>(null);
  const [medicalImages, setMedicalImages] = useState<MedicalImage[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [rehabPlan, setRehabPlan] = useState<RehabilitationPlan | null>(null);

  // Fetch patient data from API
  useEffect(() => {
    const fetchPatientData = async () => {
      if (!id) return;

      try {
        // Fetch patient details
        const patientData = await patientsAPI.getPatient(id);
        setPatient({
          id: patientData.id,
          firstName: patientData.first_name,
          lastName: patientData.last_name,
          dateOfBirth: patientData.date_of_birth,
          gender: patientData.gender,
          contactNumber: patientData.contact_number,
          email: patientData.email,
          address: patientData.address || '',
          medicalHistory: patientData.medical_history || '',
          allergies: patientData.allergies || ''
        });

        // Fetch medical images
        const images = await medicalImagesAPI.getMedicalImages({ patient_id: id });
        setMedicalImages(images.map((img: any) => ({
          id: img.id,
          imageType: img.image_type,
          bodyPart: img.body_part,
          takenDate: img.taken_date,
          description: img.description,
          imageUrl: img.image_url
        })));

        // Fetch appointments
        const appointments = await appointmentsAPI.getAppointments({ patient_id: id });
        setAppointments(appointments.map((apt: any) => ({
          id: apt.id,
          date: apt.appointment_date,
          reason: apt.reason,
          status: apt.status,
          notes: apt.notes
        })));

        // Fetch rehabilitation plans
        const rehabPlans = await rehabilitationAPI.getRehabilitationPlans({ patient_id: id });
        if (rehabPlans.length > 0) {
          const plan = rehabPlans[0]; // Get the most recent plan
          setRehabPlan({
            id: plan.id,
            title: plan.title,
            description: plan.description,
            startDate: plan.start_date,
            endDate: plan.end_date,
            status: plan.status,
            progress: plan.progress || 0,
            exercises: plan.exercises || []
          });
        }

      } catch (error) {
        console.error('Error fetching patient data:', error);
        // Fall back to mock data if API fails
        setPatient(mockPatient);
        setMedicalImages(mockMedicalImages);
        setAppointments(mockAppointments);
        setRehabPlan(mockRehabPlan);
      }
    };

    fetchPatientData();
  }, [id]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleGeneratePDF = async () => {
    try {
      if (!id) return;

      const pdfBlob = await reportsAPI.generatePatientPDF(id);

      // Create download link
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `patient-${patient.firstName}-${patient.lastName}-report.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  if (!patient) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Typography>Loading patient data...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => navigate('/patients')} sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          {`${patient.firstName} ${patient.lastName}`}
        </Typography>
        <Button
          variant="outlined"
          startIcon={<EditIcon />}
          sx={{ mr: 2 }}
          onClick={() => alert('Edit functionality would be implemented in a real application')}
        >
          Edit
        </Button>
        <Button
          variant="contained"
          startIcon={<DownloadIcon />}
          onClick={handleGeneratePDF}
        >
          Generate PDF
        </Button>
      </Box>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" color="text.secondary">
                Date of Birth
              </Typography>
              <Typography variant="body1">
                {new Date(patient.dateOfBirth).toLocaleDateString()}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" color="text.secondary">
                Gender
              </Typography>
              <Typography variant="body1">{patient.gender}</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" color="text.secondary">
                Contact Number
              </Typography>
              <Typography variant="body1">{patient.contactNumber}</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" color="text.secondary">
                Email
              </Typography>
              <Typography variant="body1">{patient.email}</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" color="text.secondary">
                Address
              </Typography>
              <Typography variant="body1">{patient.address || 'N/A'}</Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Paper elevation={3}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="patient information tabs"
          variant="fullWidth"
        >
          <Tab icon={<ImageIcon />} label="Medical History" />
          <Tab icon={<ImageIcon />} label="Medical Images" />
          <Tab icon={<EventIcon />} label="Appointments" />
          <Tab icon={<FitnessCenterIcon />} label="Rehabilitation" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6">Medical History</Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {patient.medicalHistory || 'No medical history recorded.'}
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6">Allergies</Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {patient.allergies || 'No allergies recorded.'}
              </Typography>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Medical Images</Typography>
            <Button
              variant="contained"
              onClick={() => alert('Upload functionality would be implemented in a real application')}
            >
              Upload Image
            </Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Type</TableCell>
                  <TableCell>Body Part</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {medicalImages.map((image) => (
                  <TableRow key={image.id}>
                    <TableCell>{image.imageType}</TableCell>
                    <TableCell>{image.bodyPart}</TableCell>
                    <TableCell>{new Date(image.takenDate).toLocaleDateString()}</TableCell>
                    <TableCell>{image.description || 'N/A'}</TableCell>
                    <TableCell>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => alert('View functionality would be implemented in a real application')}
                      >
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Appointments</Typography>
            <Button
              variant="contained"
              onClick={() => alert('Schedule functionality would be implemented in a real application')}
            >
              Schedule Appointment
            </Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Title</TableCell>
                  <TableCell>Date & Time</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Notes</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {appointments.map((appointment) => (
                  <TableRow key={appointment.id}>
                    <TableCell>{appointment.title}</TableCell>
                    <TableCell>
                      {new Date(appointment.appointmentDate).toLocaleString()}
                    </TableCell>
                    <TableCell>{appointment.status}</TableCell>
                    <TableCell>{appointment.notes || 'N/A'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          {rehabPlan ? (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">{rehabPlan.title}</Typography>
                <Button
                  variant="contained"
                  onClick={() => alert('Update functionality would be implemented in a real application')}
                >
                  Update Plan
                </Button>
              </Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" color="text.secondary">
                    Status
                  </Typography>
                  <Typography variant="body1">{rehabPlan.status}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" color="text.secondary">
                    Duration
                  </Typography>
                  <Typography variant="body1">
                    {`${new Date(rehabPlan.startDate).toLocaleDateString()} - ${
                      rehabPlan.endDate
                        ? new Date(rehabPlan.endDate).toLocaleDateString()
                        : 'Ongoing'
                    }`}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" color="text.secondary">
                    Description
                  </Typography>
                  <Typography variant="body1">
                    {rehabPlan.description || 'No description provided.'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                    Exercises
                  </Typography>
                  <List>
                    {rehabPlan.exercises.map((exercise) => (
                      <div key={exercise.id}>
                        <ListItem>
                          <ListItemText
                            primary={exercise.name}
                            secondary={`${exercise.frequency} - ${exercise.duration}`}
                          />
                        </ListItem>
                        <Divider />
                      </div>
                    ))}
                  </List>
                </Grid>
              </Grid>
            </>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                No Rehabilitation Plan
              </Typography>
              <Button
                variant="contained"
                onClick={() => alert('Create functionality would be implemented in a real application')}
              >
                Create Rehabilitation Plan
              </Button>
            </Box>
          )}
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default PatientDetail;
