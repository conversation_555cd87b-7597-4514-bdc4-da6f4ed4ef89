import { useEffect, useRef, useState } from 'react';
import { Box, Button, ButtonGroup, CircularProgress, Paper, Typography } from '@mui/material';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  PanTool as PanToolIcon,
  Contrast as ContrastIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import * as cornerstone from 'cornerstone-core';
import * as cornerstoneTools from 'cornerstone-tools';
import * as cornerstoneWADOImageLoader from 'cornerstone-wado-image-loader';
import * as dicomParser from 'dicom-parser';

// Define missing types for window
declare global {
  interface Window {
    Hammer: any;
    cornerstoneMath: any;
  }
}

// Initialize cornerstone tools
cornerstoneTools.external.cornerstone = cornerstone;
// These might not be available in the browser, so we'll check first
if (typeof window !== 'undefined') {
  cornerstoneTools.external.Hammer = window.Hammer || null;
  cornerstoneTools.external.cornerstoneMath = window.cornerstoneMath || null;
}
cornerstoneTools.init({ showSVGCursors: true });

// Initialize WADO image loader
cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
cornerstoneWADOImageLoader.external.dicomParser = dicomParser;

// Only initialize web workers if we're in a browser environment
if (typeof navigator !== 'undefined') {
  try {
    cornerstoneWADOImageLoader.webWorkerManager.initialize({
      maxWebWorkers: navigator.hardwareConcurrency || 1,
      startWebWorkersOnDemand: true,
    });
  } catch (error) {
    console.error('Error initializing cornerstone web workers:', error);
  }
}

interface DicomViewerProps {
  imageUrl: string;
  imageId?: string;
}

const DicomViewer = ({ imageUrl, imageId }: DicomViewerProps) => {
  const viewerRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeToolName, setActiveToolName] = useState('Wwwc'); // Default tool: window level

  useEffect(() => {
    if (!viewerRef.current) return;

    // Function to initialize the viewer
    const initializeViewer = async () => {
      try {
        // Enable the element for cornerstone
        cornerstone.enable(viewerRef.current!);

        // Load the image
        const imageIdForCornerstone = imageId || `wadouri:${imageUrl}`;

        // For demo purposes, if no real image is provided, show a placeholder
        if (!imageUrl && !imageId) {
          setError('No image URL or ID provided. This is a placeholder viewer.');
          setLoading(false);
          return;
        }

        const image = await cornerstone.loadImage(imageIdForCornerstone);

        // Display the image
        cornerstone.displayImage(viewerRef.current!, image);
        setLoading(false);

        // Initialize tools
        initializeTools();
      } catch (err) {
        console.error('Error loading image:', err);
        setError('Failed to load the DICOM image. Please check if the file is valid.');
        setLoading(false);
      }
    };

    // Initialize the viewer
    initializeViewer();

    // Clean up
    return () => {
      if (viewerRef.current) {
        try {
          cornerstone.disable(viewerRef.current);
        } catch (error) {
          console.error('Error disabling cornerstone:', error);
        }
      }
    };
  }, [imageUrl, imageId]);

  const initializeTools = () => {
    if (!viewerRef.current) return;

    try {
      // Add the tools we want to use
      cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
      cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
      cornerstoneTools.addTool(cornerstoneTools.PanTool);
      cornerstoneTools.addTool(cornerstoneTools.LengthTool);
      cornerstoneTools.addTool(cornerstoneTools.AngleTool);
      cornerstoneTools.addTool(cornerstoneTools.MagnifyTool);

      // Set the active tool
      setToolActive('Wwwc');
    } catch (error) {
      console.error('Error initializing cornerstone tools:', error);
      setError('Failed to initialize image tools. Some features may not work properly.');
    }
  };

  const setToolActive = (toolName: string) => {
    if (!viewerRef.current) return;

    try {
      // Deactivate all tools
      cornerstoneTools.setToolDisabled('Wwwc');
      cornerstoneTools.setToolDisabled('Zoom');
      cornerstoneTools.setToolDisabled('Pan');
      cornerstoneTools.setToolDisabled('Length');
      cornerstoneTools.setToolDisabled('Angle');
      cornerstoneTools.setToolDisabled('Magnify');

      // Activate the selected tool
      cornerstoneTools.setToolActive(toolName, { mouseButtonMask: 1 });
      setActiveToolName(toolName);
    } catch (error) {
      console.error('Error setting active tool:', error);
    }
  };

  const handleZoomIn = () => {
    if (!viewerRef.current) return;
    try {
      const viewport = cornerstone.getViewport(viewerRef.current);
      viewport.scale += 0.25;
      cornerstone.setViewport(viewerRef.current, viewport);
    } catch (error) {
      console.error('Error zooming in:', error);
    }
  };

  const handleZoomOut = () => {
    if (!viewerRef.current) return;
    try {
      const viewport = cornerstone.getViewport(viewerRef.current);
      viewport.scale -= 0.25;
      cornerstone.setViewport(viewerRef.current, viewport);
    } catch (error) {
      console.error('Error zooming out:', error);
    }
  };

  const handleReset = () => {
    if (!viewerRef.current) return;
    try {
      cornerstone.reset(viewerRef.current);
    } catch (error) {
      console.error('Error resetting view:', error);
    }
  };

  return (
    <Box sx={{ width: '100%', height: '100%', position: 'relative' }}>
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 10,
          }}
        >
          <CircularProgress color="primary" />
        </Box>
      )}

      {error && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f5f5f5',
            zIndex: 10,
            p: 2,
          }}
        >
          <Paper elevation={3} sx={{ p: 3, textAlign: 'center', maxWidth: '80%' }}>
            <Typography variant="h6" color="error" gutterBottom>
              Error Loading Image
            </Typography>
            <Typography variant="body1">{error}</Typography>
          </Paper>
        </Box>
      )}

      <Box
        sx={{
          position: 'absolute',
          top: 10,
          left: 10,
          zIndex: 5,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderRadius: 1,
          p: 0.5,
        }}
      >
        <ButtonGroup variant="contained" size="small">
          <Button
            onClick={() => setToolActive('Wwwc')}
            color={activeToolName === 'Wwwc' ? 'primary' : 'inherit'}
            startIcon={<ContrastIcon />}
          >
            Level
          </Button>
          <Button
            onClick={() => setToolActive('Zoom')}
            color={activeToolName === 'Zoom' ? 'primary' : 'inherit'}
            startIcon={<ZoomInIcon />}
          >
            Zoom
          </Button>
          <Button
            onClick={() => setToolActive('Pan')}
            color={activeToolName === 'Pan' ? 'primary' : 'inherit'}
            startIcon={<PanToolIcon />}
          >
            Pan
          </Button>
        </ButtonGroup>
      </Box>

      <Box
        sx={{
          position: 'absolute',
          bottom: 10,
          right: 10,
          zIndex: 5,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderRadius: 1,
          p: 0.5,
        }}
      >
        <ButtonGroup variant="contained" size="small">
          <Button onClick={handleZoomIn}>
            <ZoomInIcon />
          </Button>
          <Button onClick={handleZoomOut}>
            <ZoomOutIcon />
          </Button>
          <Button onClick={handleReset}>
            <RefreshIcon />
          </Button>
        </ButtonGroup>
      </Box>

      <div
        ref={viewerRef}
        style={{
          width: '100%',
          height: '100%',
          minHeight: '400px',
          backgroundColor: '#000',
        }}
      />
    </Box>
  );
};

export default DicomViewer;
