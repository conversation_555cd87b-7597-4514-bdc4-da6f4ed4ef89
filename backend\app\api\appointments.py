from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta

from app.database.database import get_db
from app.models.models import Appointment, Patient
from app.schemas.schemas import AppointmentCreate, AppointmentUpdate, AppointmentResponse
from app.auth.auth import get_current_active_user

router = APIRouter()

@router.post("/", response_model=AppointmentResponse, status_code=status.HTTP_201_CREATED)
def create_appointment(
    appointment: AppointmentCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    try:
        # Check if patient exists
        db_patient = db.query(Patient).filter(Patient.id == appointment.patient_id).first()
        if not db_patient:
            raise HTTPException(status_code=404, detail="Patient not found")

        # Validate appointment data
        if appointment.duration_minutes <= 0:
            raise HTTPException(status_code=400, detail="Duration must be greater than 0")

        if appointment.appointment_date < datetime.now():
            raise HTTPException(status_code=400, detail="Appointment date cannot be in the past")

        # Check for appointment conflicts (optional - can be disabled for demo)
        appointment_end = appointment.appointment_date + timedelta(minutes=appointment.duration_minutes)

        # Get existing appointments that might conflict
        existing_appointments = db.query(Appointment).filter(
            Appointment.status.in_(["Scheduled", "Confirmed"]),
            Appointment.appointment_date < appointment_end,
        ).all()

        # Check for conflicts manually
        conflicts = []
        for existing in existing_appointments:
            existing_end = existing.appointment_date + timedelta(minutes=existing.duration_minutes)
            if existing_end > appointment.appointment_date:
                conflicts.append(existing)

        # For demo purposes, we'll allow conflicts but log them
        if conflicts:
            print(f"Warning: Appointment conflicts detected but allowing creation for demo purposes")

        db_appointment = Appointment(**appointment.model_dump())
        db.add(db_appointment)
        db.commit()
        db.refresh(db_appointment)
        return db_appointment

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"Error creating appointment: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/", response_model=List[AppointmentResponse])
def get_appointments(
    patient_id: Optional[str] = None,
    status: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    query = db.query(Appointment)

    if patient_id:
        query = query.filter(Appointment.patient_id == patient_id)

    if status:
        query = query.filter(Appointment.status == status)

    if start_date:
        query = query.filter(Appointment.appointment_date >= start_date)

    if end_date:
        query = query.filter(Appointment.appointment_date <= end_date)

    return query.order_by(Appointment.appointment_date).offset(skip).limit(limit).all()

@router.get("/{appointment_id}", response_model=AppointmentResponse)
def get_appointment(
    appointment_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_appointment = db.query(Appointment).filter(Appointment.id == appointment_id).first()
    if db_appointment is None:
        raise HTTPException(status_code=404, detail="Appointment not found")
    return db_appointment

@router.put("/{appointment_id}", response_model=AppointmentResponse)
def update_appointment(
    appointment_id: str,
    appointment: AppointmentUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_appointment = db.query(Appointment).filter(Appointment.id == appointment_id).first()
    if db_appointment is None:
        raise HTTPException(status_code=404, detail="Appointment not found")

    update_data = appointment.model_dump(exclude_unset=True)

    # If appointment date or duration is being updated, check for conflicts
    if "appointment_date" in update_data or "duration_minutes" in update_data:
        new_date = update_data.get("appointment_date", db_appointment.appointment_date)
        new_duration = update_data.get("duration_minutes", db_appointment.duration_minutes)

        appointment_end = new_date + timedelta(minutes=new_duration)

        # Get existing appointments that might conflict (excluding current appointment)
        existing_appointments = db.query(Appointment).filter(
            Appointment.id != appointment_id,
            Appointment.status != "Cancelled"
        ).all()

        # Check for conflicts manually
        conflicts = []
        for existing in existing_appointments:
            existing_end = existing.appointment_date + timedelta(minutes=existing.duration_minutes)
            if (existing.appointment_date < appointment_end and
                existing_end > new_date):
                conflicts.append(existing)

        if conflicts:
            raise HTTPException(status_code=400, detail="Updated appointment time conflicts with existing appointments")

    for key, value in update_data.items():
        setattr(db_appointment, key, value)

    db_appointment.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_appointment)
    return db_appointment

@router.delete("/{appointment_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_appointment(
    appointment_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    db_appointment = db.query(Appointment).filter(Appointment.id == appointment_id).first()
    if db_appointment is None:
        raise HTTPException(status_code=404, detail="Appointment not found")

    db.delete(db_appointment)
    db.commit()
    return None
