#!/usr/bin/env python3
"""
Update the default user information to reflect <PERSON><PERSON>
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import SessionLocal
from app.models.models import User

def update_default_user():
    """Update the default user information"""
    db = SessionLocal()
    try:
        # Find the existing user
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not existing_user:
            print("✗ Default user not found")
            return
        
        # Update user information
        existing_user.full_name = "<PERSON><PERSON>"
        
        db.commit()
        db.refresh(existing_user)
        
        print("✓ User information updated successfully")
        print(f"  Email: {existing_user.email}")
        print(f"  Full Name: {existing_user.full_name}")
        
    except Exception as e:
        print(f"✗ Error updating user: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """Main function"""
    print("Updating user information...")
    print("=" * 50)
    
    update_default_user()
    
    print("=" * 50)
    print("✓ User update completed!")

if __name__ == "__main__":
    main()
