# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
venv/
.env

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.npm
.yarn-integrity
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Frontend build
frontend/dist/
frontend/.vite/

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Uploads
uploads/
media/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local
