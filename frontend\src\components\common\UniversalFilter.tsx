import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Collapse,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Typography,
  Stack,
  Chip,
  Avatar,
  Tooltip,
  InputAdornment,
  useTheme,
  useMediaQuery,
  alpha,
  Di<PERSON>r,
  Badge,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Tune as TuneIcon,
  Sort as SortIcon,
} from '@mui/icons-material';

export interface FilterOption {
  value: string;
  label: string;
  color?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  icon?: React.ReactNode;
  avatar?: string;
  count?: number;
}

export interface FilterField {
  key: string;
  label: string;
  type: 'select' | 'text' | 'date' | 'dateRange' | 'multiSelect';
  options?: FilterOption[];
  placeholder?: string;
  width?: number; // Grid width (1-12)
}

export interface SortOption {
  value: string;
  label: string;
  direction?: 'asc' | 'desc';
}

export interface UniversalFilterProps {
  // Search
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
  showSearch?: boolean;

  // Filters
  filters: FilterField[];
  filterValues: Record<string, any>;
  onFilterChange: (key: string, value: any) => void;

  // Sort
  sortOptions?: SortOption[];
  sortValue?: string;
  onSortChange?: (value: string) => void;

  // Results
  totalCount: number;
  filteredCount: number;

  // UI
  title?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  showResultCount?: boolean;

  // Actions
  onClearAll?: () => void;
  customActions?: React.ReactNode;
}

const UniversalFilter: React.FC<UniversalFilterProps> = ({
  searchValue = '',
  onSearchChange,
  searchPlaceholder = 'Search...',
  showSearch = true,
  filters,
  filterValues,
  onFilterChange,
  sortOptions,
  sortValue,
  onSortChange,
  totalCount,
  filteredCount,
  title = 'Search & Filter',
  collapsible = true,
  defaultExpanded = false,
  showResultCount = true,
  onClearAll,
  customActions,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [expanded, setExpanded] = useState(defaultExpanded);

  // Count active filters
  const activeFilterCount = Object.values(filterValues).filter(value =>
    value !== '' && value !== null && value !== undefined &&
    (Array.isArray(value) ? value.length > 0 : true)
  ).length;

  const hasActiveFilters = activeFilterCount > 0 || searchValue !== '';

  const handleClearAll = () => {
    if (onSearchChange) onSearchChange('');
    filters.forEach(filter => {
      onFilterChange(filter.key, filter.type === 'multiSelect' ? [] : '');
    });
    if (onSortChange) onSortChange('');
    if (onClearAll) onClearAll();
  };

  const renderFilterField = (filter: FilterField) => {
    const value = filterValues[filter.key] || (filter.type === 'multiSelect' ? [] : '');

    switch (filter.type) {
      case 'select':
        return (
          <FormControl fullWidth size="small">
            <InputLabel>{filter.label}</InputLabel>
            <Select
              value={value}
              label={filter.label}
              onChange={(e) => onFilterChange(filter.key, e.target.value)}
              sx={{ borderRadius: 2 }}
            >
              <MenuItem value="">
                <Typography color="text.secondary">All {filter.label}</Typography>
              </MenuItem>
              {filter.options?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Stack direction="row" alignItems="center" spacing={1} sx={{ width: '100%' }}>
                    {option.avatar && (
                      <Avatar sx={{ width: 24, height: 24, fontSize: '0.75rem' }}>
                        {option.avatar}
                      </Avatar>
                    )}
                    {option.icon && <Box sx={{ display: 'flex', alignItems: 'center' }}>{option.icon}</Box>}
                    <Typography sx={{ flex: 1 }}>{option.label}</Typography>
                    {option.color && (
                      <Chip
                        size="small"
                        label={option.label}
                        color={option.color}
                        variant="outlined"
                        sx={{ ml: 'auto' }}
                      />
                    )}
                    {option.count !== undefined && (
                      <Chip
                        size="small"
                        label={option.count}
                        variant="outlined"
                        sx={{ ml: 'auto', minWidth: 'auto' }}
                      />
                    )}
                  </Stack>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'text':
        return (
          <TextField
            fullWidth
            size="small"
            label={filter.label}
            placeholder={filter.placeholder}
            value={value}
            onChange={(e) => onFilterChange(filter.key, e.target.value)}
            sx={{
              '& .MuiOutlinedInput-root': { borderRadius: 2 }
            }}
          />
        );

      case 'date':
        return (
          <TextField
            fullWidth
            size="small"
            type="date"
            label={filter.label}
            value={value}
            onChange={(e) => onFilterChange(filter.key, e.target.value)}
            InputLabelProps={{ shrink: true }}
            sx={{
              '& .MuiOutlinedInput-root': { borderRadius: 2 }
            }}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Card
      elevation={0}
      sx={{
        mb: 3,
        borderRadius: 3,
        border: '1px solid',
        borderColor: hasActiveFilters ? 'primary.main' : 'divider',
        transition: 'border-color 0.2s ease-in-out',
        background: hasActiveFilters
          ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.02)} 0%, ${alpha(theme.palette.primary.main, 0.01)} 100%)`
          : 'background.paper'
      }}
    >
      <CardContent sx={{ pb: expanded ? 2 : '16px !important' }}>
        {/* Header */}
        <Stack direction="row" alignItems="center" justifyContent="space-between" spacing={2}>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <TuneIcon color="primary" />
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {title}
              </Typography>
            </Stack>

            {hasActiveFilters && (
              <Badge badgeContent={activeFilterCount} color="primary">
                <Chip
                  icon={<FilterListIcon />}
                  label="Active"
                  color="primary"
                  variant="outlined"
                  size="small"
                />
              </Badge>
            )}
          </Stack>

          <Stack direction="row" alignItems="center" spacing={1}>
            {showResultCount && (
              <Typography variant="body2" color="text.secondary">
                {filteredCount} of {totalCount} results
              </Typography>
            )}

            {customActions}

            {hasActiveFilters && (
              <Tooltip title="Clear all filters">
                <IconButton size="small" onClick={handleClearAll}>
                  <ClearIcon />
                </IconButton>
              </Tooltip>
            )}

            {collapsible && (
              <IconButton
                size="small"
                onClick={() => setExpanded(!expanded)}
                sx={{
                  transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.2s ease-in-out'
                }}
              >
                <ExpandMoreIcon />
              </IconButton>
            )}
          </Stack>
        </Stack>

        {/* Search Bar - Always Visible */}
        {showSearch && (
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              size="small"
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearchChange?.(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: searchValue && (
                  <InputAdornment position="end">
                    <IconButton size="small" onClick={() => onSearchChange?.('')}>
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                ),
                sx: { borderRadius: 3 }
              }}
            />
          </Box>
        )}

        {/* Collapsible Filters */}
        <Collapse in={expanded || !collapsible}>
          <Box sx={{ mt: 2 }}>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              {/* Filter Fields */}
              {filters.map((filter) => (
                <Grid item xs={12} sm={6} md={filter.width || 4} key={filter.key}>
                  {renderFilterField(filter)}
                </Grid>
              ))}

              {/* Sort Options */}
              {sortOptions && sortOptions.length > 0 && (
                <Grid item xs={12} sm={6} md={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Sort By</InputLabel>
                    <Select
                      value={sortValue || ''}
                      label="Sort By"
                      onChange={(e) => onSortChange?.(e.target.value)}
                      sx={{ borderRadius: 2 }}
                      startAdornment={<SortIcon sx={{ mr: 1, color: 'action.active' }} />}
                    >
                      {sortOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              )}

              {/* Clear All Button */}
              {hasActiveFilters && (
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="primary"
                    onClick={handleClearAll}
                    sx={{
                      height: 40,
                      borderRadius: 2,
                      borderStyle: 'dashed'
                    }}
                  >
                    Clear All Filters
                  </Button>
                </Grid>
              )}
            </Grid>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default UniversalFilter;
export type { FilterField, FilterOption, SortOption, UniversalFilterProps };
