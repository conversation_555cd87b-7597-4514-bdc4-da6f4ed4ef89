# Orthopedic EHR System

**Dr. <PERSON><PERSON><PERSON><PERSON>**
*Additional Professor, Department of Orthopaedics*
*Post Graduate Institute of Medical Education and Research (PGIMER)*
*Chandigarh, India*

A lightweight yet comprehensive web application tailored for orthopedic practice management, designed to handle patient records, medical images, appointments, and rehabilitation tracking.

## Features

### Patient Record Management
- Centralized dashboard for managing patient profiles
- Personal details, medical history, and treatment notes
- Add, edit, and delete patient records

### Medical Image Management
- Upload and organize medical images (X-rays, MRIs, CT scans)
- DICOM file support with built-in viewer
- Annotation capabilities

### Appointment Scheduling
- Calendar integration for scheduling and managing appointments
- Filter appointments by date, patient, or status
- Appointment details and notes

### Rehabilitation Tracking
- Create and manage rehabilitation plans
- Track patient progress with charts and milestones
- Exercise management and progress reporting

### PDF Export and Sharing
- Generate detailed PDF reports for patient records
- Include images and treatment summaries

### Security & Accessibility
- Authentication and authorization
- Responsive design for desktop, tablet, and mobile devices

## Tech Stack

### Frontend
- React.js with TypeScript
- Material-UI for responsive design
- React Router for navigation
- React Query for data fetching
- Cornerstone.js for DICOM image viewing

### Backend
- FastAPI (Python)
- SQLAlchemy ORM
- JWT Authentication
- Pydantic for data validation

### Database
- SQLite (development)
- PostgreSQL (production)

## Getting Started

### Prerequisites
- Node.js (v16+)
- Python (v3.8+)
- npm or yarn

### Installation

#### Backend Setup
1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Create and activate a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Run the server:
   ```
   uvicorn main:app --reload
   ```

#### Frontend Setup
1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

### Demo Credentials
- Email: <EMAIL>
- Password: password
- User: Dr. Siddhartha Sharma

## API Documentation

Once the backend server is running, you can access the API documentation at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Project Structure

```
EHR-Ortho/
├── backend/                # FastAPI backend
│   ├── app/
│   │   ├── api/            # API endpoints
│   │   ├── auth/           # Authentication
│   │   ├── database/       # Database configuration
│   │   ├── models/         # SQLAlchemy models
│   │   ├── schemas/        # Pydantic schemas
│   │   └── utils/          # Utility functions
│   ├── main.py             # Application entry point
│   └── requirements.txt    # Python dependencies
│
├── frontend/               # React frontend
│   ├── public/             # Static files
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── context/        # React context
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── App.tsx         # Main application component
│   │   └── main.tsx        # Entry point
│   ├── package.json        # Node.js dependencies
│   └── vite.config.ts      # Vite configuration
│
└── README.md               # Project documentation
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Cornerstone.js](https://github.com/cornerstonejs/cornerstone) for DICOM image viewing
- [Material-UI](https://mui.com/) for the UI components
- [FastAPI](https://fastapi.tiangolo.com/) for the backend framework
