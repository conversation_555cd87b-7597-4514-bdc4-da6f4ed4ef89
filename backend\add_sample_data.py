#!/usr/bin/env python3
"""
Add sample data to the database for testing
"""

import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import SessionLocal
from app.models.models import <PERSON><PERSON>, Appoint<PERSON>, RehabilitationPlan, MedicalImage

def add_sample_patients():
    """Add sample patients to the database"""
    db = SessionLocal()
    try:
        # Check if patients already exist
        existing_patients = db.query(Patient).count()
        if existing_patients > 0:
            print(f"✓ Database already has {existing_patients} patients")
            return

        # Sample patients
        patients = [
            {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "date_of_birth": "1980-05-15",
                "gender": "Male",
                "contact_number": "+1-555-0101",
                "email": "<EMAIL>",
                "address": "123 Main St, Anytown, USA",
                "medical_history": "Previous knee injury in 2019. No major surgeries.",
                "allergies": "Penicillin"
            },
            {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "date_of_birth": "1975-08-22",
                "gender": "Female",
                "contact_number": "+1-555-0102",
                "email": "<EMAIL>",
                "address": "456 Oak Ave, Somewhere, USA",
                "medical_history": "Chronic back pain. Physical therapy in 2020.",
                "allergies": "None known"
            },
            {
                "first_name": "<PERSON>",
                "last_name": "Brown",
                "date_of_birth": "1990-12-03",
                "gender": "Male",
                "contact_number": "+1-555-0103",
                "email": "<EMAIL>",
                "address": "789 Pine Rd, Elsewhere, USA",
                "medical_history": "Sports-related shoulder injury. Active lifestyle.",
                "allergies": "Latex"
            },
            {
                "first_name": "Sarah",
                "last_name": "Davis",
                "date_of_birth": "1985-03-18",
                "gender": "Female",
                "contact_number": "+1-555-0104",
                "email": "<EMAIL>",
                "address": "321 Elm St, Nowhere, USA",
                "medical_history": "Hip replacement surgery in 2021. Recovering well.",
                "allergies": "Aspirin"
            },
            {
                "first_name": "Robert",
                "last_name": "Wilson",
                "date_of_birth": "1970-11-30",
                "gender": "Male",
                "contact_number": "+1-555-0105",
                "email": "<EMAIL>",
                "address": "654 Maple Dr, Anywhere, USA",
                "medical_history": "Arthritis in both knees. Regular exercise routine.",
                "allergies": "None known"
            }
        ]

        for patient_data in patients:
            patient = Patient(**patient_data)
            db.add(patient)

        db.commit()
        print(f"✓ Added {len(patients)} sample patients")

    except Exception as e:
        print(f"✗ Error adding sample patients: {e}")
        db.rollback()
    finally:
        db.close()

def add_sample_appointments():
    """Add sample appointments"""
    db = SessionLocal()
    try:
        # Check if appointments already exist
        existing_appointments = db.query(Appointment).count()
        if existing_appointments > 0:
            print(f"✓ Database already has {existing_appointments} appointments")
            return

        # Get patients
        patients = db.query(Patient).all()
        if not patients:
            print("✗ No patients found. Add patients first.")
            return

        # Sample appointments for today and tomorrow
        today = datetime.now()
        tomorrow = today + timedelta(days=1)

        appointments = []

        # Add appointments based on available patients
        if len(patients) >= 1:
            appointments.append({
                "patient_id": patients[0].id,
                "appointment_date": today.replace(hour=9, minute=0, second=0, microsecond=0),
                "duration_minutes": 60,
                "title": "Follow-up consultation",
                "description": "Regular check-up appointment",
                "status": "Scheduled",
                "notes": "Regular check-up"
            })

        if len(patients) >= 2:
            appointments.append({
                "patient_id": patients[1].id,
                "appointment_date": today.replace(hour=14, minute=30, second=0, microsecond=0),
                "duration_minutes": 45,
                "title": "Physical therapy session",
                "description": "Therapy session for rehabilitation",
                "status": "Scheduled",
                "notes": "Therapy session"
            })

        if len(patients) >= 3:
            appointments.append({
                "patient_id": patients[2].id,
                "appointment_date": tomorrow.replace(hour=10, minute=0, second=0, microsecond=0),
                "duration_minutes": 30,
                "title": "Examination",
                "description": "General medical examination",
                "status": "Scheduled",
                "notes": "General examination"
            })

        # If we only have one patient, add multiple appointments for them
        if len(patients) == 1:
            appointments.append({
                "patient_id": patients[0].id,
                "appointment_date": tomorrow.replace(hour=11, minute=0, second=0, microsecond=0),
                "duration_minutes": 30,
                "title": "Follow-up",
                "description": "Follow-up appointment",
                "status": "Scheduled",
                "notes": "Follow-up appointment"
            })

        for apt_data in appointments:
            appointment = Appointment(**apt_data)
            db.add(appointment)

        db.commit()
        print(f"✓ Added {len(appointments)} sample appointments")

    except Exception as e:
        print(f"✗ Error adding sample appointments: {e}")
        db.rollback()
    finally:
        db.close()

def add_sample_rehab_plans():
    """Add sample rehabilitation plans"""
    db = SessionLocal()
    try:
        # Check if rehab plans already exist
        existing_plans = db.query(RehabilitationPlan).count()
        if existing_plans > 0:
            print(f"✓ Database already has {existing_plans} rehabilitation plans")
            return

        # Get patients
        patients = db.query(Patient).all()
        if not patients:
            print("✗ No patients found. Add patients first.")
            return

        # Sample rehabilitation plans
        plans = []

        # Add plans based on available patients
        if len(patients) >= 1:
            plans.append({
                "patient_id": patients[0].id,
                "title": "Rehabilitation Program",
                "description": "Comprehensive rehabilitation program",
                "start_date": datetime.now(),
                "end_date": (datetime.now() + timedelta(days=90)),
                "status": "Active"
            })

        if len(patients) >= 2:
            plans.append({
                "patient_id": patients[1].id,
                "title": "Physical Therapy Program",
                "description": "Physical therapy and exercise program",
                "start_date": datetime.now(),
                "end_date": (datetime.now() + timedelta(days=60)),
                "status": "Active"
            })

        for plan_data in plans:
            plan = RehabilitationPlan(**plan_data)
            db.add(plan)

        db.commit()
        print(f"✓ Added {len(plans)} sample rehabilitation plans")

    except Exception as e:
        print(f"✗ Error adding sample rehabilitation plans: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """Main function to add all sample data"""
    print("Adding sample data to the database...")
    print("=" * 50)

    add_sample_patients()
    add_sample_appointments()
    add_sample_rehab_plans()

    print("=" * 50)
    print("✓ Sample data added successfully!")
    print("\nYou can now test the application with sample data.")

if __name__ == "__main__":
    main()
