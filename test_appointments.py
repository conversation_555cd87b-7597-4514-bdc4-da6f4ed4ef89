#!/usr/bin/env python3
"""
Test script to verify appointments API is working
"""

import requests
import json
from datetime import datetime, timedelta

# API base URL
BASE_URL = "http://localhost:8000/api"

def get_auth_token():
    """Get authentication token"""
    response = requests.post(
        f"{BASE_URL}/auth/token",
        data={
            "username": "<EMAIL>",
            "password": "password"
        },
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"Failed to get token: {response.status_code} - {response.text}")
        return None

def test_appointments_api():
    """Test the appointments API"""
    token = get_auth_token()
    if not token:
        return

    headers = {"Authorization": f"Bearer {token}"}

    print("Testing Appointments API...")
    print("=" * 50)

    # 1. Get all appointments
    print("1. Getting all appointments...")
    response = requests.get(f"{BASE_URL}/appointments/", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        appointments = response.json()
        print(f"Found {len(appointments)} appointments")
        for apt in appointments:
            print(f"  - {apt['title']} on {apt['appointment_date']}")
    else:
        print(f"Error: {response.text}")

    print()

    # 2. Get all patients (needed for creating appointments)
    print("2. Getting all patients...")
    response = requests.get(f"{BASE_URL}/patients/", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        patients = response.json()
        print(f"Found {len(patients)} patients")
        if patients:
            patient_id = patients[0]['id']
            print(f"Using patient: {patients[0]['first_name']} {patients[0]['last_name']} (ID: {patient_id})")

            # 3. Create a new appointment
            print("\n3. Creating a new appointment...")
            # Use a time that won't conflict with existing appointments
            future_date = datetime.now() + timedelta(days=3)
            future_date = future_date.replace(hour=14, minute=0, second=0, microsecond=0)
            appointment_data = {
                "patient_id": patient_id,
                "title": "Test Appointment",
                "description": "This is a test appointment created via API",
                "appointment_date": future_date.isoformat(),
                "duration_minutes": 30,
                "status": "Scheduled",
                "notes": "Created by test script"
            }

            response = requests.post(
                f"{BASE_URL}/appointments/",
                json=appointment_data,
                headers=headers
            )
            print(f"Status: {response.status_code}")
            if response.status_code == 201:
                new_appointment = response.json()
                print(f"Created appointment: {new_appointment['title']} (ID: {new_appointment['id']})")

                # 4. Update the appointment
                print("\n4. Updating the appointment...")
                update_data = {
                    "title": "Updated Test Appointment",
                    "notes": "Updated by test script"
                }
                response = requests.put(
                    f"{BASE_URL}/appointments/{new_appointment['id']}",
                    json=update_data,
                    headers=headers
                )
                print(f"Status: {response.status_code}")
                if response.status_code == 200:
                    updated_appointment = response.json()
                    print(f"Updated appointment: {updated_appointment['title']}")

                # 5. Get the specific appointment
                print("\n5. Getting the specific appointment...")
                response = requests.get(
                    f"{BASE_URL}/appointments/{new_appointment['id']}",
                    headers=headers
                )
                print(f"Status: {response.status_code}")
                if response.status_code == 200:
                    appointment = response.json()
                    print(f"Retrieved appointment: {appointment['title']}")

                # 6. Delete the appointment
                print("\n6. Deleting the test appointment...")
                response = requests.delete(
                    f"{BASE_URL}/appointments/{new_appointment['id']}",
                    headers=headers
                )
                print(f"Status: {response.status_code}")
                if response.status_code == 200:
                    print("Appointment deleted successfully")
            else:
                print(f"Error creating appointment: {response.text}")
    else:
        print(f"Error getting patients: {response.text}")

    print("\n" + "=" * 50)
    print("Appointments API test completed!")

if __name__ == "__main__":
    test_appointments_api()
