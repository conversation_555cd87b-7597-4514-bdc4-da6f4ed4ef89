import { useState } from 'react';
import {
  Box,
  Button,
  ButtonGroup,
  Paper,
  Typography,
  Slider,
  Stack,
} from '@mui/material';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  PanTool as PanToolIcon,
  Contrast as ContrastIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';

interface MockDicomViewerProps {
  imageUrl?: string;
  title?: string;
}

/**
 * A mock DICOM viewer component that doesn't rely on Cornerstone libraries.
 * This is useful for development and testing when actual DICOM viewing is not required.
 */
const MockDicomViewer = ({ imageUrl, title = 'DICOM Image' }: MockDicomViewerProps) => {
  const [brightness, setBrightness] = useState<number>(50);
  const [contrast, setContrast] = useState<number>(50);
  const [zoom, setZoom] = useState<number>(100);
  const [activeTool, setActiveTool] = useState<string>('window');

  const handleBrightnessChange = (_event: Event, newValue: number | number[]) => {
    setBrightness(newValue as number);
  };

  const handleContrastChange = (_event: Event, newValue: number | number[]) => {
    setContrast(newValue as number);
  };

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 10, 200));
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 10, 50));
  };

  const handleReset = () => {
    setBrightness(50);
    setContrast(50);
    setZoom(100);
  };

  return (
    <Box sx={{ width: '100%', height: '100%', position: 'relative' }}>
      <Box
        sx={{
          position: 'absolute',
          top: 10,
          left: 10,
          zIndex: 5,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderRadius: 1,
          p: 0.5,
        }}
      >
        <ButtonGroup variant="contained" size="small">
          <Button
            onClick={() => setActiveTool('window')}
            color={activeTool === 'window' ? 'primary' : 'inherit'}
            startIcon={<ContrastIcon />}
          >
            Level
          </Button>
          <Button
            onClick={() => setActiveTool('zoom')}
            color={activeTool === 'zoom' ? 'primary' : 'inherit'}
            startIcon={<ZoomInIcon />}
          >
            Zoom
          </Button>
          <Button
            onClick={() => setActiveTool('pan')}
            color={activeTool === 'pan' ? 'primary' : 'inherit'}
            startIcon={<PanToolIcon />}
          >
            Pan
          </Button>
        </ButtonGroup>
      </Box>

      <Box
        sx={{
          position: 'absolute',
          bottom: 10,
          right: 10,
          zIndex: 5,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderRadius: 1,
          p: 0.5,
        }}
      >
        <ButtonGroup variant="contained" size="small">
          <Button onClick={handleZoomIn}>
            <ZoomInIcon />
          </Button>
          <Button onClick={handleZoomOut}>
            <ZoomOutIcon />
          </Button>
          <Button onClick={handleReset}>
            <RefreshIcon />
          </Button>
        </ButtonGroup>
      </Box>

      <Paper
        elevation={3}
        sx={{
          width: '100%',
          height: '100%',
          minHeight: '400px',
          backgroundColor: '#000',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {imageUrl ? (
          <Box
            component="img"
            src={imageUrl}
            alt="Medical image"
            sx={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
              transform: `scale(${zoom / 100})`,
              filter: `brightness(${brightness}%) contrast(${contrast}%)`,
              transition: 'transform 0.2s ease-in-out',
            }}
          />
        ) : (
          <Box
            sx={{
              width: '80%',
              height: '80%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              border: '2px dashed #555',
              borderRadius: 2,
              p: 3,
            }}
          >
            <Typography variant="h6" color="#aaa" gutterBottom>
              {title}
            </Typography>
            <Typography variant="body2" color="#888" align="center">
              This is a mock DICOM viewer for development purposes.
              <br />
              No actual image is loaded.
            </Typography>
          </Box>
        )}

        {activeTool === 'window' && (
          <Box
            sx={{
              position: 'absolute',
              bottom: 20,
              left: 20,
              width: 300,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              p: 2,
              borderRadius: 2,
            }}
          >
            <Stack spacing={2}>
              <Box>
                <Typography variant="caption" color="white">
                  Brightness: {brightness}%
                </Typography>
                <Slider
                  value={brightness}
                  onChange={handleBrightnessChange}
                  min={0}
                  max={200}
                  sx={{ color: 'white' }}
                />
              </Box>
              <Box>
                <Typography variant="caption" color="white">
                  Contrast: {contrast}%
                </Typography>
                <Slider
                  value={contrast}
                  onChange={handleContrastChange}
                  min={0}
                  max={200}
                  sx={{ color: 'white' }}
                />
              </Box>
            </Stack>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default MockDicomViewer;
