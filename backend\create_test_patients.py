#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create test patients for the Medical Images functionality
"""
import sys
import os
sys.path.append(os.getcwd())

from app.database.database import SessionLocal
from app.models.models import Patient
from datetime import datetime, date

def create_test_patients():
    db = SessionLocal()
    try:
        # Check if we already have enough test patients
        existing_patients = db.query(Patient).count()
        if existing_patients >= 5:
            print(f"Found {existing_patients} existing patients. Skipping creation.")
            return

        # Create test patients
        test_patients = [
            {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "email": "<EMAIL>",
                "contact_number": "+91 98765 43210",
                "date_of_birth": date(1980, 5, 15),
                "gender": "Male",
                "address": "123 Main Street, Chandigarh",
                "emergency_contact": "<PERSON> - +91 98765 43211",
                "medical_history": "Previous knee surgery in 2020"
            },
            {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "email": "<EMAIL>",
                "contact_number": "+91 98765 43212",
                "date_of_birth": date(1975, 8, 22),
                "gender": "Female",
                "address": "456 Oak Avenue, <PERSON>di<PERSON>",
                "emergency_contact": "<PERSON> <PERSON> - +91 98765 43213",
                "medical_history": "Shoulder dislocation, chronic back pain"
            },
            {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "email": "<EMAIL>",
                "contact_number": "+91 98765 43214",
                "date_of_birth": date(1965, 12, 10),
                "gender": "Male",
                "address": "789 Pine Road, Chandigarh",
                "emergency_contact": "Sarah Brown - +91 98765 43215",
                "medical_history": "Hip replacement surgery, arthritis"
            },
            {
                "first_name": "Sarah",
                "last_name": "Davis",
                "email": "<EMAIL>",
                "contact_number": "+91 98765 43216",
                "date_of_birth": date(1990, 3, 8),
                "gender": "Female",
                "address": "321 Elm Street, Chandigarh",
                "emergency_contact": "David Davis - +91 98765 43217",
                "medical_history": "ACL tear, sports injury"
            },
            {
                "first_name": "Robert",
                "last_name": "Wilson",
                "email": "<EMAIL>",
                "contact_number": "+91 98765 43218",
                "date_of_birth": date(1955, 7, 30),
                "gender": "Male",
                "address": "654 Maple Drive, Chandigarh",
                "emergency_contact": "Linda Wilson - +91 98765 43219",
                "medical_history": "Spinal stenosis, chronic pain"
            }
        ]

        created_patients = []
        for patient_data in test_patients:
            patient = Patient(**patient_data)
            db.add(patient)
            created_patients.append(patient)

        db.commit()

        print(f"Successfully created {len(created_patients)} test patients:")
        for patient in created_patients:
            db.refresh(patient)
            print(f"- {patient.first_name} {patient.last_name} (ID: {patient.id})")

    except Exception as e:
        print(f"Error creating test patients: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_patients()
