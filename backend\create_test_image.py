#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a test medical image for demonstration
"""
import sys
import os
sys.path.append(os.getcwd())

from PIL import Image, ImageDraw, ImageFont
import io

def create_test_medical_image():
    """Create a simple test medical image that looks like an X-ray"""
    
    # Create a black background (like an X-ray)
    width, height = 800, 600
    image = Image.new('RGB', (width, height), color='black')
    draw = ImageDraw.Draw(image)
    
    # Draw some bone-like structures in white/gray
    # Draw a simple knee joint representation
    
    # Femur (thigh bone)
    draw.rectangle([350, 50, 450, 300], fill='lightgray', outline='white', width=2)
    
    # Tibia (shin bone)
    draw.rectangle([340, 320, 430, 550], fill='lightgray', outline='white', width=2)
    
    # Fibula (smaller bone)
    draw.rectangle([460, 330, 480, 540], fill='gray', outline='white', width=1)
    
    # Joint space
    draw.rectangle([340, 300, 450, 320], fill='darkgray')
    
    # Add some medical annotations
    try:
        # Try to use a default font, fallback to basic if not available
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # Add labels
    draw.text((500, 100), "FEMUR", fill='white', font=font)
    draw.text((500, 350), "TIBIA", fill='white', font=font)
    draw.text((500, 450), "FIBULA", fill='white', font=font)
    
    # Add patient info (simulated)
    draw.text((50, 50), "Patient: Test Patient", fill='white', font=font)
    draw.text((50, 80), "Date: 2024-01-15", fill='white', font=font)
    draw.text((50, 110), "Type: X-Ray Knee", fill='white', font=font)
    
    # Add grid lines for measurement
    for i in range(0, width, 50):
        draw.line([(i, 0), (i, height)], fill='darkgray', width=1)
    for i in range(0, height, 50):
        draw.line([(0, i), (width, i)], fill='darkgray', width=1)
    
    # Save the image
    os.makedirs('test_images', exist_ok=True)
    image_path = 'test_images/test_knee_xray.jpg'
    image.save(image_path, 'JPEG', quality=95)
    
    print(f"Test medical image created: {image_path}")
    return image_path

if __name__ == "__main__":
    create_test_medical_image()
