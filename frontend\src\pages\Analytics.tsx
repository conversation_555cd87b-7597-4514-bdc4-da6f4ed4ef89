import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Typography,
  Paper,
  Stack,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  LinearProgress,
  CircularProgress,
  Alert,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  LocalHospital as LocalHospitalIcon,
  CalendarToday as CalendarTodayIcon,
  Assessment as AssessmentIcon,
  BarChart as BarChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Timeline as TimelineIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  FilterList as FilterListIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  Elderly as ElderlyIcon,
  ChildCare as ChildCareIcon,
  MedicalServices as MedicalServicesIcon,
  EventAvailable as EventAvailableIcon,
  EventBusy as EventBusyIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler,
} from 'chart.js';
import { Bar, Pie, Line, Doughnut } from 'react-chartjs-2';
import { patientsAPI, appointmentsAPI, medicalImagesAPI, rehabilitationAPI } from '../services/api';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler
);

interface AnalyticsData {
  patients: any[];
  appointments: any[];
  medicalImages: any[];
  rehabilitationPlans: any[];
}

interface PatientStats {
  total: number;
  byGender: { [key: string]: number };
  byAgeGroup: { [key: string]: number };
  newThisMonth: number;
  averageAge: number;
}

interface AppointmentStats {
  total: number;
  byStatus: { [key: string]: number };
  thisWeek: number;
  thisMonth: number;
  completionRate: number;
}

interface MedicalImageStats {
  total: number;
  byType: { [key: string]: number };
  byBodyPart: { [key: string]: number };
  thisMonth: number;
}

interface RehabilitationStats {
  total: number;
  active: number;
  completed: number;
  averageProgress: number;
  completionRate: number;
}

const Analytics = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<AnalyticsData>({
    patients: [],
    appointments: [],
    medicalImages: [],
    rehabilitationPlans: [],
  });
  const [timeRange, setTimeRange] = useState('month'); // week, month, quarter, year
  const [chartType, setChartType] = useState('overview'); // overview, detailed

  // Fetch all data
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      setLoading(true);
      setError(null);

      try {
        const [patients, appointments, medicalImages, rehabilitationPlans] = await Promise.all([
          patientsAPI.getPatients().catch(() => []),
          appointmentsAPI.getAppointments().catch(() => []),
          medicalImagesAPI.getMedicalImages().catch(() => []),
          rehabilitationAPI.getRehabilitationPlans().catch(() => []),
        ]);

        setData({
          patients,
          appointments,
          medicalImages,
          rehabilitationPlans,
        });
      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError('Failed to load analytics data. Some features may not be available.');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, []);

  // Calculate patient statistics
  const patientStats: PatientStats = useMemo(() => {
    const patients = data.patients;

    const byGender = patients.reduce((acc, patient) => {
      acc[patient.gender] = (acc[patient.gender] || 0) + 1;
      return acc;
    }, {});

    const byAgeGroup = patients.reduce((acc, patient) => {
      const age = new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear();
      let ageGroup;
      if (age < 18) ageGroup = 'Children (0-17)';
      else if (age < 35) ageGroup = 'Young Adults (18-34)';
      else if (age < 55) ageGroup = 'Adults (35-54)';
      else if (age < 75) ageGroup = 'Seniors (55-74)';
      else ageGroup = 'Elderly (75+)';

      acc[ageGroup] = (acc[ageGroup] || 0) + 1;
      return acc;
    }, {});

    const thisMonth = new Date();
    thisMonth.setDate(1);
    const newThisMonth = patients.filter(p =>
      new Date(p.created_at) >= thisMonth
    ).length;

    const totalAge = patients.reduce((sum, patient) => {
      const age = new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear();
      return sum + age;
    }, 0);

    return {
      total: patients.length,
      byGender,
      byAgeGroup,
      newThisMonth,
      averageAge: patients.length > 0 ? Math.round(totalAge / patients.length) : 0,
    };
  }, [data.patients]);

  // Calculate appointment statistics
  const appointmentStats: AppointmentStats = useMemo(() => {
    const appointments = data.appointments;

    const byStatus = appointments.reduce((acc, appointment) => {
      acc[appointment.status] = (acc[appointment.status] || 0) + 1;
      return acc;
    }, {});

    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1);

    const thisWeek = appointments.filter(a =>
      new Date(a.appointmentDate) >= weekAgo
    ).length;

    const thisMonth = appointments.filter(a =>
      new Date(a.appointmentDate) >= monthAgo
    ).length;

    const completed = byStatus['Completed'] || 0;
    const total = appointments.length;
    const completionRate = total > 0 ? (completed / total) * 100 : 0;

    return {
      total,
      byStatus,
      thisWeek,
      thisMonth,
      completionRate,
    };
  }, [data.appointments]);

  // Calculate medical image statistics
  const medicalImageStats: MedicalImageStats = useMemo(() => {
    const images = data.medicalImages;

    const byType = images.reduce((acc, image) => {
      acc[image.image_type] = (acc[image.image_type] || 0) + 1;
      return acc;
    }, {});

    const byBodyPart = images.reduce((acc, image) => {
      acc[image.body_part] = (acc[image.body_part] || 0) + 1;
      return acc;
    }, {});

    const thisMonth = new Date();
    thisMonth.setDate(1);
    const thisMonthCount = images.filter(img =>
      new Date(img.taken_date) >= thisMonth
    ).length;

    return {
      total: images.length,
      byType,
      byBodyPart,
      thisMonth: thisMonthCount,
    };
  }, [data.medicalImages]);

  // Calculate rehabilitation statistics
  const rehabilitationStats: RehabilitationStats = useMemo(() => {
    const plans = data.rehabilitationPlans;

    const active = plans.filter(p => p.status === 'Active').length;
    const completed = plans.filter(p => p.status === 'Completed').length;

    const totalProgress = plans.reduce((sum, plan) => sum + (plan.progress || 0), 0);
    const averageProgress = plans.length > 0 ? totalProgress / plans.length : 0;

    const completionRate = plans.length > 0 ? (completed / plans.length) * 100 : 0;

    return {
      total: plans.length,
      active,
      completed,
      averageProgress,
      completionRate,
    };
  }, [data.rehabilitationPlans]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading Analytics...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`,
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems={{ xs: 'flex-start', sm: 'center' }} spacing={2}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'success.main' }}>
              Healthcare Analytics Dashboard
            </Typography>
            <Typography variant="body1" sx={{ color: 'text.secondary', mb: 2 }}>
              Comprehensive insights and statistics for Dr. Siddhartha Sharma's orthopedic practice
            </Typography>
            <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
              <Chip
                icon={<PeopleIcon />}
                label={`${patientStats.total} Total Patients`}
                color="primary"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<EventAvailableIcon />}
                label={`${appointmentStats.total} Appointments`}
                color="info"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<MedicalServicesIcon />}
                label={`${medicalImageStats.total} Medical Images`}
                color="secondary"
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<AssessmentIcon />}
                label={`${rehabilitationStats.total} Rehab Plans`}
                color="success"
                variant="outlined"
                size="small"
              />
            </Stack>
          </Box>
          <Stack direction="row" spacing={1}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={(e) => setTimeRange(e.target.value)}
              >
                <MenuItem value="week">This Week</MenuItem>
                <MenuItem value="month">This Month</MenuItem>
                <MenuItem value="quarter">This Quarter</MenuItem>
                <MenuItem value="year">This Year</MenuItem>
              </Select>
            </FormControl>
            <Tooltip title="Refresh Data">
              <IconButton
                onClick={() => window.location.reload()}
                sx={{
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export Report">
              <IconButton
                sx={{
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                }}
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
      </Paper>

      {error && (
        <Alert severity="warning" sx={{ mb: 3, borderRadius: 2 }}>
          {error}
        </Alert>
      )}

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Patient Overview */}
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main', width: 48, height: 48 }}>
                  <PeopleIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                  {patientStats.total}
                </Typography>
              </Stack>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                Total Patients
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Average age: {patientStats.averageAge} years
              </Typography>
              <Stack direction="row" alignItems="center" spacing={1}>
                <TrendingUpIcon color="success" fontSize="small" />
                <Typography variant="body2" color="success.main" sx={{ fontWeight: 600 }}>
                  +{patientStats.newThisMonth} this month
                </Typography>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Appointment Overview */}
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main', width: 48, height: 48 }}>
                  <CalendarTodayIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                  {appointmentStats.total}
                </Typography>
              </Stack>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                Appointments
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {appointmentStats.completionRate.toFixed(1)}% completion rate
              </Typography>
              <Stack direction="row" alignItems="center" spacing={1}>
                <EventAvailableIcon color="info" fontSize="small" />
                <Typography variant="body2" color="info.main" sx={{ fontWeight: 600 }}>
                  {appointmentStats.thisWeek} this week
                </Typography>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Medical Images Overview */}
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                <Avatar sx={{ bgcolor: 'secondary.main', width: 48, height: 48 }}>
                  <MedicalServicesIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                  {medicalImageStats.total}
                </Typography>
              </Stack>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                Medical Images
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {Object.keys(medicalImageStats.byType).length} different types
              </Typography>
              <Stack direction="row" alignItems="center" spacing={1}>
                <AssessmentIcon color="secondary" fontSize="small" />
                <Typography variant="body2" color="secondary.main" sx={{ fontWeight: 600 }}>
                  {medicalImageStats.thisMonth} this month
                </Typography>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Rehabilitation Overview */}
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider', height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main', width: 48, height: 48 }}>
                  <LocalHospitalIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                  {rehabilitationStats.total}
                </Typography>
              </Stack>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                Rehab Plans
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {rehabilitationStats.averageProgress.toFixed(1)}% avg progress
              </Typography>
              <Stack direction="row" alignItems="center" spacing={1}>
                <CheckCircleIcon color="success" fontSize="small" />
                <Typography variant="body2" color="success.main" sx={{ fontWeight: 600 }}>
                  {rehabilitationStats.active} active plans
                </Typography>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Patient Demographics - Gender Distribution */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider', height: '100%' }}>
            <CardHeader
              title="Patient Demographics"
              subheader="Gender Distribution"
              action={
                <IconButton>
                  <PieChartIcon />
                </IconButton>
              }
            />
            <CardContent>
              <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                {Object.keys(patientStats.byGender).length > 0 ? (
                  <Pie
                    data={{
                      labels: Object.keys(patientStats.byGender),
                      datasets: [
                        {
                          data: Object.values(patientStats.byGender),
                          backgroundColor: [
                            theme.palette.primary.main,
                            theme.palette.secondary.main,
                            theme.palette.info.main,
                          ],
                          borderWidth: 2,
                          borderColor: theme.palette.background.paper,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                        },
                      },
                    }}
                  />
                ) : (
                  <Typography color="text.secondary">No data available</Typography>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Age Group Distribution */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider', height: '100%' }}>
            <CardHeader
              title="Age Distribution"
              subheader="Patients by Age Groups"
              action={
                <IconButton>
                  <BarChartIcon />
                </IconButton>
              }
            />
            <CardContent>
              <Box sx={{ height: 300 }}>
                {Object.keys(patientStats.byAgeGroup).length > 0 ? (
                  <Bar
                    data={{
                      labels: Object.keys(patientStats.byAgeGroup),
                      datasets: [
                        {
                          label: 'Number of Patients',
                          data: Object.values(patientStats.byAgeGroup),
                          backgroundColor: alpha(theme.palette.primary.main, 0.7),
                          borderColor: theme.palette.primary.main,
                          borderWidth: 1,
                          borderRadius: 4,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: false,
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          ticks: {
                            stepSize: 1,
                          },
                        },
                      },
                    }}
                  />
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                    <Typography color="text.secondary">No data available</Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Appointment Status Distribution */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider', height: '100%' }}>
            <CardHeader
              title="Appointment Status"
              subheader="Current Status Distribution"
              action={
                <IconButton>
                  <PieChartIcon />
                </IconButton>
              }
            />
            <CardContent>
              <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                {Object.keys(appointmentStats.byStatus).length > 0 ? (
                  <Doughnut
                    data={{
                      labels: Object.keys(appointmentStats.byStatus),
                      datasets: [
                        {
                          data: Object.values(appointmentStats.byStatus),
                          backgroundColor: [
                            theme.palette.success.main,
                            theme.palette.info.main,
                            theme.palette.error.main,
                            theme.palette.warning.main,
                          ],
                          borderWidth: 2,
                          borderColor: theme.palette.background.paper,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                        },
                      },
                    }}
                  />
                ) : (
                  <Typography color="text.secondary">No data available</Typography>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Medical Image Types */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider', height: '100%' }}>
            <CardHeader
              title="Medical Images"
              subheader="Distribution by Type"
              action={
                <IconButton>
                  <BarChartIcon />
                </IconButton>
              }
            />
            <CardContent>
              <Box sx={{ height: 300 }}>
                {Object.keys(medicalImageStats.byType).length > 0 ? (
                  <Bar
                    data={{
                      labels: Object.keys(medicalImageStats.byType),
                      datasets: [
                        {
                          label: 'Number of Images',
                          data: Object.values(medicalImageStats.byType),
                          backgroundColor: alpha(theme.palette.secondary.main, 0.7),
                          borderColor: theme.palette.secondary.main,
                          borderWidth: 1,
                          borderRadius: 4,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: false,
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          ticks: {
                            stepSize: 1,
                          },
                        },
                      },
                    }}
                  />
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                    <Typography color="text.secondary">No data available</Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Patient Statistics Table */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider' }}>
            <CardHeader
              title="Patient Statistics"
              subheader="Detailed breakdown by demographics"
            />
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <PeopleIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Total Patients"
                    secondary={`${patientStats.total} registered patients`}
                  />
                  <Typography variant="h6" color="primary.main">
                    {patientStats.total}
                  </Typography>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <MaleIcon color="info" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Male Patients"
                    secondary={`${((patientStats.byGender['Male'] || 0) / patientStats.total * 100).toFixed(1)}% of total`}
                  />
                  <Typography variant="h6" color="info.main">
                    {patientStats.byGender['Male'] || 0}
                  </Typography>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <FemaleIcon color="secondary" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Female Patients"
                    secondary={`${((patientStats.byGender['Female'] || 0) / patientStats.total * 100).toFixed(1)}% of total`}
                  />
                  <Typography variant="h6" color="secondary.main">
                    {patientStats.byGender['Female'] || 0}
                  </Typography>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <TrendingUpIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="New This Month"
                    secondary="Recently registered patients"
                  />
                  <Typography variant="h6" color="success.main">
                    {patientStats.newThisMonth}
                  </Typography>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <ElderlyIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Average Age"
                    secondary="Mean age of all patients"
                  />
                  <Typography variant="h6" color="warning.main">
                    {patientStats.averageAge} years
                  </Typography>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Appointment & Treatment Statistics */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider' }}>
            <CardHeader
              title="Treatment Statistics"
              subheader="Appointments and rehabilitation progress"
            />
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CalendarTodayIcon color="info" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Total Appointments"
                    secondary="All scheduled appointments"
                  />
                  <Typography variant="h6" color="info.main">
                    {appointmentStats.total}
                  </Typography>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Completion Rate"
                    secondary="Successfully completed appointments"
                  />
                  <Typography variant="h6" color="success.main">
                    {appointmentStats.completionRate.toFixed(1)}%
                  </Typography>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <EventAvailableIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary="This Week"
                    secondary="Appointments scheduled this week"
                  />
                  <Typography variant="h6" color="primary.main">
                    {appointmentStats.thisWeek}
                  </Typography>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <LocalHospitalIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Active Rehab Plans"
                    secondary="Currently active rehabilitation plans"
                  />
                  <Typography variant="h6" color="success.main">
                    {rehabilitationStats.active}
                  </Typography>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <AssessmentIcon color="secondary" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Avg Rehab Progress"
                    secondary="Average rehabilitation completion"
                  />
                  <Typography variant="h6" color="secondary.main">
                    {rehabilitationStats.averageProgress.toFixed(1)}%
                  </Typography>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Medical Imaging Insights */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Card elevation={0} sx={{ borderRadius: 3, border: '1px solid', borderColor: 'divider' }}>
            <CardHeader
              title="Medical Imaging Insights"
              subheader="Comprehensive analysis of medical imaging data"
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Avatar sx={{ bgcolor: 'info.main', width: 64, height: 64, mx: 'auto', mb: 2 }}>
                      <MedicalServicesIcon sx={{ fontSize: 32 }} />
                    </Avatar>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main', mb: 1 }}>
                      {medicalImageStats.total}
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      Total Images
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Across all imaging modalities
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Avatar sx={{ bgcolor: 'secondary.main', width: 64, height: 64, mx: 'auto', mb: 2 }}>
                      <BarChartIcon sx={{ fontSize: 32 }} />
                    </Avatar>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main', mb: 1 }}>
                      {Object.keys(medicalImageStats.byType).length}
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      Image Types
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Different imaging modalities
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Avatar sx={{ bgcolor: 'success.main', width: 64, height: 64, mx: 'auto', mb: 2 }}>
                      <TrendingUpIcon sx={{ fontSize: 32 }} />
                    </Avatar>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main', mb: 1 }}>
                      {medicalImageStats.thisMonth}
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      This Month
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Recent imaging studies
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              {/* Body Part Distribution */}
              {Object.keys(medicalImageStats.byBodyPart).length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Most Common Body Parts Imaged
                  </Typography>
                  <Grid container spacing={2}>
                    {Object.entries(medicalImageStats.byBodyPart)
                      .sort(([,a], [,b]) => (b as number) - (a as number))
                      .slice(0, 6)
                      .map(([bodyPart, count], index) => (
                        <Grid item xs={12} sm={6} md={4} key={bodyPart}>
                          <Box sx={{
                            p: 2,
                            border: '1px solid',
                            borderColor: 'divider',
                            borderRadius: 2,
                            bgcolor: alpha(theme.palette.primary.main, 0.05)
                          }}>
                            <Stack direction="row" justifyContent="space-between" alignItems="center">
                              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                {bodyPart}
                              </Typography>
                              <Chip
                                label={count}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                            </Stack>
                            <LinearProgress
                              variant="determinate"
                              value={(count as number) / Math.max(...Object.values(medicalImageStats.byBodyPart)) * 100}
                              sx={{ mt: 1, height: 6, borderRadius: 3 }}
                              color="primary"
                            />
                          </Box>
                        </Grid>
                      ))}
                  </Grid>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Analytics;
