import { useState } from 'react';
import { Navigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  TextField,
  Typography,
  Paper,
  Avatar,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import { LockOutlined as LockOutlinedIcon } from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const Login = () => {
  const { login, error: authError, loading, isAuthenticated } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState('');

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');

    // Basic form validation
    if (!email) {
      setFormError('Email is required');
      return;
    }

    if (!password) {
      setFormError('Password is required');
      return;
    }

    // Call login from auth context
    await login(email, password);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        bgcolor: 'background.default',
      }}
    >
      {/* Left Side - Login Form */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: { xs: 2, sm: 4 },
          bgcolor: 'background.paper',
        }}
      >
        <Container maxWidth="sm">
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            <Paper
              elevation={3}
              sx={{
                p: { xs: 3, sm: 4 },
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%',
                borderRadius: 3,
                boxShadow: '0 8px 40px rgba(0, 0, 0, 0.08)',
                border: '1px solid',
                borderColor: 'divider',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 3,
                }}
              >
                <Avatar
                  src="/dr-sharma-profile.svg"
                  sx={{
                    bgcolor: 'primary.main',
                    width: 50,
                    height: 50,
                    mr: 2,
                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  DS
                </Avatar>
                <Box>
                  <Typography component="h1" variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                    Ortho EHR
                  </Typography>
                  <Typography variant="subtitle1" sx={{ color: 'text.secondary', fontWeight: 500 }}>
                    PGIMER Chandigarh
                  </Typography>
                </Box>
              </Box>

            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Typography component="h2" variant="h6" sx={{ color: 'text.primary', fontWeight: 600 }}>
                Dr. Siddhartha Sharma
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1 }}>
                Additional Professor, Department of Orthopaedics
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 500 }}>
                Sign in to access the EHR system
              </Typography>
            </Box>

            {/* Display form validation errors */}
            {formError && (
              <Alert
                severity="error"
                sx={{
                  mt: 1,
                  mb: 2,
                  width: '100%',
                  borderRadius: 1,
                }}
              >
                {formError}
              </Alert>
            )}

            {/* Display authentication errors */}
            {authError && (
              <Alert
                severity="error"
                sx={{
                  mt: 1,
                  mb: 2,
                  width: '100%',
                  borderRadius: 1,
                }}
              >
                {authError}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1, width: '100%' }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="email"
                label="Email Address"
                name="email"
                autoComplete="email"
                autoFocus
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                error={!!formError && !email}
                sx={{ mb: 2 }}
                InputProps={{
                  sx: { borderRadius: 1 }
                }}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label="Password"
                type="password"
                id="password"
                autoComplete="current-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                error={!!formError && !password}
                sx={{ mb: 2 }}
                InputProps={{
                  sx: { borderRadius: 1 }
                }}
              />
              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                sx={{
                  mt: 2,
                  mb: 3,
                  py: 1.5,
                  borderRadius: 1,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                }}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <CircularProgress size={24} sx={{ mr: 1 }} color="inherit" />
                    Signing in...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>

              <Divider sx={{ my: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Demo Access
                </Typography>
              </Divider>

              <Box sx={{
                p: 2,
                bgcolor: 'background.default',
                borderRadius: 1,
                border: '1px dashed',
                borderColor: 'divider',
              }}>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 1 }}>
                  Use these credentials for demo access:
                </Typography>
                <Typography variant="body2" fontWeight="medium" align="center">
                  Email: <EMAIL><br />
                  Password: password
                </Typography>
              </Box>
              </Box>
            </Paper>
          </Box>
        </Container>
      </Box>

      {/* Right Side - Medical Information */}
      <Box
        sx={{
          flex: 1,
          display: { xs: 'none', md: 'flex' },
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'primary.main',
          backgroundImage: 'linear-gradient(135deg, rgba(25, 118, 210, 0.95) 0%, rgba(0, 150, 136, 0.95) 100%)',
          color: 'white',
          p: 4,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.3,
          }}
        />

        <Box sx={{ textAlign: 'center', zIndex: 1, maxWidth: 500 }}>
          <Avatar
            src="/dr-sharma-profile.svg"
            sx={{
              width: 120,
              height: 120,
              mx: 'auto',
              mb: 3,
              border: '4px solid rgba(255, 255, 255, 0.3)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
            }}
          >
            DS
          </Avatar>

          <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
            Dr. Siddhartha Sharma
          </Typography>

          <Typography variant="h6" sx={{ mb: 1, opacity: 0.9 }}>
            Additional Professor
          </Typography>

          <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
            Department of Orthopaedics
          </Typography>

          <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.3)', mb: 4 }} />

          <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
            Orthopedic EHR System
          </Typography>

          <Typography variant="body1" sx={{ mb: 4, opacity: 0.9, lineHeight: 1.6 }}>
            Advanced Electronic Health Records system designed specifically for orthopedic practice management,
            featuring patient records, medical imaging, appointment scheduling, and rehabilitation tracking.
          </Typography>

          <Box sx={{ textAlign: 'left', maxWidth: 400, mx: 'auto' }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Key Features:
            </Typography>
            <Box component="ul" sx={{ pl: 2, '& li': { mb: 1, opacity: 0.9 } }}>
              <li>Comprehensive Patient Management</li>
              <li>DICOM Medical Imaging Support</li>
              <li>Appointment Scheduling System</li>
              <li>Rehabilitation Progress Tracking</li>
              <li>Secure Data Management</li>
            </Box>
          </Box>

          <Box sx={{ mt: 6, pt: 4, borderTop: '1px solid rgba(255, 255, 255, 0.2)' }}>
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Post Graduate Institute of Medical Education and Research
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.7 }}>
              Chandigarh, India
            </Typography>
            <Typography variant="body2" sx={{ mt: 2, opacity: 0.6 }}>
              © {new Date().getFullYear()} PGIMER. All rights reserved.
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Login;
