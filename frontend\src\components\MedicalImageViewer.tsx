import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  IconButton,
  Paper,
  Typography,
  Slider,
  Stack,
  Tooltip,
  CircularProgress,
  Alert,
} from '@mui/material';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './DicomViewer/DicomViewer';
import MockDicomViewer from './DicomViewer/MockDicomViewer';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  Download as DownloadIcon,
  Brightness6 as BrightnessIcon,
  Contrast as ContrastIcon,
  PanTool as PanToolIcon,
} from '@mui/icons-material';

interface MedicalImageViewerProps {
  imageId: string;
  imageType: string;
  title?: string;
}

const MedicalImageViewer: React.FC<MedicalImageViewerProps> = ({
  imageId,
  imageType,
  title = 'Medical Image'
}) => {
  const [zoom, setZoom] = useState(100);
  const [brightness, setBrightness] = useState(100);
  const [contrast, setContrast] = useState(100);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string>('');
  const [isDicom, setIsDicom] = useState(false);
  const [panMode, setPanMode] = useState(false);
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadImage = async () => {
      setLoading(true);
      setError(null);

      try {
        // Get the image URL from the backend
        const token = localStorage.getItem('token');
        const response = await fetch(`http://localhost:8000/api/medical-images/file/${imageId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to load image');
        }

        // Get content type to determine file type
        const contentType = response.headers.get('content-type') || '';
        const blob = await response.blob();

        // Check if it's a DICOM file
        const isDicomFile = contentType.includes('application/dicom') ||
                           imageType.toLowerCase() === 'dicom' ||
                           blob.type.includes('application/dicom');

        setIsDicom(isDicomFile);
        setFileType(contentType);

        // Create a blob URL for the image
        const url = URL.createObjectURL(blob);
        setImageUrl(url);
      } catch (err) {
        console.error('Error loading image:', err);
        setError('Failed to load the medical image. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (imageId) {
      loadImage();
    }

    // Cleanup blob URL on unmount
    return () => {
      if (imageUrl) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [imageId]);

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 500));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25));
  };

  const handleReset = () => {
    setZoom(100);
    setBrightness(100);
    setContrast(100);
    setPanPosition({ x: 0, y: 0 });
  };

  const handleDownload = () => {
    if (imageUrl) {
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = `medical-image-${imageId}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleFullscreen = () => {
    if (containerRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        containerRef.current.requestFullscreen();
      }
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (panMode) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - panPosition.x,
        y: e.clientY - panPosition.y,
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && panMode) {
      setPanPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  if (loading) {
    return (
      <Box
        sx={{
          height: 400,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.default',
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2,
        }}
      >
        <Stack alignItems="center" spacing={2}>
          <CircularProgress size={40} />
          <Typography variant="body2" color="text.secondary">
            Loading medical image...
          </Typography>
        </Stack>
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        sx={{
          height: 400,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.default',
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2,
        }}
      >
        <Alert severity="error" sx={{ maxWidth: 400 }}>
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      {/* Toolbar */}
      <Paper elevation={1} sx={{ p: 2, mb: 2, borderRadius: 1 }}>
        <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
          <Stack direction="row" spacing={0}>
            <Tooltip title="Zoom In">
              <IconButton onClick={handleZoomIn} size="small" sx={{ borderRadius: 0, borderRight: '1px solid', borderColor: 'divider' }}>
                <ZoomInIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Zoom Out">
              <IconButton onClick={handleZoomOut} size="small" sx={{ borderRadius: 0, borderRight: '1px solid', borderColor: 'divider' }}>
                <ZoomOutIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Reset View">
              <IconButton onClick={handleReset} size="small" sx={{ borderRadius: 0 }}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Stack>

          <Tooltip title="Pan Mode">
            <IconButton
              onClick={() => setPanMode(!panMode)}
              color={panMode ? 'primary' : 'default'}
              size="small"
            >
              <PanToolIcon />
            </IconButton>
          </Tooltip>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
            <BrightnessIcon fontSize="small" />
            <Slider
              size="small"
              value={brightness}
              onChange={(_, value) => setBrightness(value as number)}
              min={50}
              max={150}
              sx={{ width: 80 }}
            />
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
            <ContrastIcon fontSize="small" />
            <Slider
              size="small"
              value={contrast}
              onChange={(_, value) => setContrast(value as number)}
              min={50}
              max={150}
              sx={{ width: 80 }}
            />
          </Box>

          <Typography variant="body2" sx={{ minWidth: 60 }}>
            {zoom}%
          </Typography>

          <Box sx={{ flexGrow: 1 }} />

          <Stack direction="row" spacing={0}>
            <Tooltip title="Download">
              <IconButton onClick={handleDownload} size="small" sx={{ borderRadius: 0, borderRight: '1px solid', borderColor: 'divider' }}>
                <DownloadIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Fullscreen">
              <IconButton onClick={handleFullscreen} size="small" sx={{ borderRadius: 0 }}>
                <FullscreenIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
      </Paper>

      {/* Image Viewer */}
      {isDicom ? (
        // DICOM Viewer
        <Box sx={{ height: 400, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
          <MockDicomViewer
            imageUrl={imageUrl || undefined}
            title={title}
          />
        </Box>
      ) : (
        // Regular Image Viewer
        <Paper
          ref={containerRef}
          elevation={0}
          sx={{
            height: 400,
            overflow: 'hidden',
            position: 'relative',
            bgcolor: '#000',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 2,
            cursor: panMode ? 'grab' : 'default',
            '&:active': {
              cursor: panMode && isDragging ? 'grabbing' : undefined,
            },
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {imageUrl && (
            <Box
              component="img"
              ref={imageRef}
              src={imageUrl}
              alt={title}
              sx={{
                maxWidth: 'none',
                maxHeight: 'none',
                width: 'auto',
                height: 'auto',
                filter: `brightness(${brightness}%) contrast(${contrast}%)`,
                transition: isDragging ? 'none' : 'transform 0.2s ease-out',
                transformOrigin: 'center center',
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: `translate(-50%, -50%) scale(${zoom / 100}) translate(${panPosition.x}px, ${panPosition.y}px)`,
                userSelect: 'none',
                pointerEvents: 'none',
              }}
              onLoad={() => setLoading(false)}
              onError={() => setError('Failed to display image')}
            />
          )}
        </Paper>
      )}
    </Box>
  );
};

export default MedicalImageViewer;
